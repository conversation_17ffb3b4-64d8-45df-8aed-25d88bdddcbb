<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" @class(['dark' => ($appearance ?? 'system') == 'dark'])>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        {{-- Inline script to detect system dark mode preference and apply it immediately --}}
        <script>
            (function() {
                const appearance = '{{ $appearance ?? "system" }}';

                if (appearance === 'system') {
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                    if (prefersDark) {
                        document.documentElement.classList.add('dark');
                    }
                }
            })();
        </script>

        {{-- Inline style to set the HTML background color based on our theme in app.css --}}
        <style>
            html {
                background-color: oklch(1 0 0);
            }

            html.dark {
                background-color: var(--background);
            }
        </style>

        <title inertia>{{ config('app.name', 'Laravel') }}</title>

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('images/logo.png') }}" type="image/png">
        <link rel="shortcut icon" href="{{ asset('images/logo.png') }}" type="image/png">
        <link rel="apple-touch-icon" href="{{ asset('images/logo.png') }}">

        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+Todhri:wght@400;500;600;700&display=swap" rel="stylesheet">

        <!-- Links to Privacy Policy for Google OAuth Verification -->
        <link rel="privacy-policy" href="{{ route('privacy.policy') }}">
        <link rel="author" href="{{ route('privacy.policy') }}">
        <!-- Standard HTML link to privacy policy -->
        <a href="{{ route('privacy.policy') }}" style="display: none;">Privacy Policy</a>

        @routes
        @viteReactRefresh
        @vite(['resources/js/app.tsx', "resources/js/pages/{$page['component']}.tsx"])
        @inertiaHead

        <!-- Google Tag Manager -->
        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-MPRJ257G');</script>
        <!-- End Google Tag Manager -->
    </head>
    <body class="font-sans antialiased">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MPRJ257G"
                      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

{{--    <div style="background-color: transparent; text-align: center; font-size: 12px;">--}}
{{--        <a href="{{ route('privacy.policy') }}" style="color: #6c757d; text-decoration: none;">Privacy Policy</a>--}}
{{--    </div>--}}
    <!-- Privacy Policy Link for Google OAuth Verification -->
{{--    <div style="position: absolute; bottom: 10px; left: 10px; z-index: 1000;">--}}
{{--        <a href="{{ route('privacy.policy') }}" style="color: rgba(107, 114, 128, 0.7); font-size: 12px; text-decoration: none;">Privacy Policy</a>--}}
{{--    </div>--}}
        @inertia
    </body>
</html>
