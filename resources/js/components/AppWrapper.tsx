import React, { ReactNode } from 'react';
import FloatingThemeToggle from './FloatingThemeToggle';
import { Toaster } from '@/components/ui/toaster';
import ImpersonationBanner from './impersonation-banner';

interface AppWrapperProps {
    children: ReactNode;
    impersonating?: boolean;
}

export default function AppWrapper({ children, impersonating = false }: AppWrapperProps) {
    return (
        <>
            {impersonating && <ImpersonationBanner />}
            {children}
            <FloatingThemeToggle />
            <Toaster />
        </>
    );
}
