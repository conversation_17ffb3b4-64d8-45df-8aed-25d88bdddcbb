import React, { useState } from 'react';
import { Link } from '@inertiajs/react';

interface HeroSectionProps {
  // You can add props here if needed
}

const HeroSection: React.FC<HeroSectionProps> = () => {
  const [email, setEmail] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission logic here
    console.log('Email submitted:', email);
    // You can add API call to subscribe the email
  };

  return (
    <section className="py-12 md:py-16">
      <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 py-4 md:py-8">
        <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-16">
          {/* Left Column - Text Content */}
          <div className="w-full lg:w-1/2">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-serif text-center lg:text-left text-gray-900 dark:text-white transition-colors duration-200">
              Your roadmap to CSV transformation
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-6 text-center lg:text-left">
              Transform your CSV files effortlessly. Import, update, and export bulk records with just a few clicks.
            </p>
            <form onSubmit={handleSubmit} className="mb-4 mx-auto lg:mx-0 max-w-md">
              <div className="flex flex-col sm:flex-row gap-3 mb-2">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-grow px-4 py-3 rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary transition-colors duration-200"
                  placeholder="Your email address"
                  required
                />
                <button
                  type="submit"
                  className="px-6 py-3 bg-primary text-white font-medium rounded-md hover:bg-primary/90 transition-colors duration-200 dark:bg-[#198856] dark:text-white dark:hover:bg-[#198856]/80 focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-[#198856]/50 dark:focus:ring-offset-gray-900"
                >
                  Subscribe
                </button>
              </div>
              <div className="text-center sm:text-left mt-2">
                <small className="text-gray-500 dark:text-gray-400">
                  By subscribing, you agree to our <Link href={route('privacy.policy')} className="text-primary dark:text-primary/90 hover:underline">Privacy Policy</Link>
                </small>
              </div>
            </form>
              <div className="text-center lg:text-left mb-6">
                  <Link href={route('privacy.policy')} className="text-sm text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-gray-300 transition-colors duration-200">
                      Privacy Policy
                  </Link>
              </div>
          </div>

          {/* Right Column - Image */}
          <div className="w-full lg:w-1/2 flex justify-center items-center">
            <img
              src="/images/hero-illustration.svg"
              alt="CSV Transformation Illustration"
              className="w-full h-auto max-h-[600px] object-contain rounded-lg"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
