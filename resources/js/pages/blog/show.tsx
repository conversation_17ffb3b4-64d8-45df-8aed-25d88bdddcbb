import React from 'react';
import AppPublicLayout from '@/layouts/app-public-layout';
import { type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import Seo from '@/components/Seo';
import { formatDistanceToNow, format } from 'date-fns';

// Related Post Card Component
interface RelatedPostCardProps {
  title: string;
  published_at: string;
  featured_image: string;
  featured_image_url?: string;
  slug: string;
}

const RelatedPostCard: React.FC<RelatedPostCardProps> = ({
  title,
  published_at,
  featured_image,
  featured_image_url,
  slug
}) => {
  const formattedDate = formatDistanceToNow(new Date(published_at), { addSuffix: true });
  return (
    <div className="flex items-start">
      <img
        src={featured_image_url || featured_image || '/images/blog/default.jpg'}
        alt={title}
        className="w-20 h-20 object-cover rounded-md mr-4"
      />
      <div>
        <Link
          href={`/blog/${slug}`}
          className="font-medium hover:text-primary transition-colors duration-200"
        >
          {title}
        </Link>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {formattedDate}
        </p>
      </div>
    </div>
  );
};

// Social Share Button Component
interface SocialShareButtonProps {
  platform: 'twitter' | 'facebook' | 'linkedin';
  url: string;
}

const SocialShareButton: React.FC<SocialShareButtonProps> = ({ platform, url }) => {
  const getIcon = () => {
    switch (platform) {
      case 'twitter':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
          </svg>
        );
      case 'facebook':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
          </svg>
        );
      case 'linkedin':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path fillRule="evenodd" d="M19.7 3H4.3A1.3 1.3 0 003 4.3v15.4A1.3 1.3 0 004.3 21h15.4a1.3 1.3 0 001.3-1.3V4.3A1.3 1.3 0 0019.7 3zM8.339 18.338H5.667v-8.59h2.672v8.59zM7.004 8.574a1.548 1.548 0 11-.002-3.096 1.548 1.548 0 01.002 3.096zm11.335 9.764H15.67v-4.177c0-.996-.017-2.278-1.387-2.278-1.389 0-1.601 1.086-1.601 2.206v4.249h-2.667v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.779 3.203 4.092v4.711z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  const getShareUrl = () => {
    switch (platform) {
      case 'twitter':
        return `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}`;
      case 'facebook':
        return `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
      case 'linkedin':
        return `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
    }
  };

  return (
    <a
      href={getShareUrl()}
      target="_blank"
      rel="noopener noreferrer"
      className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors duration-200"
    >
      {getIcon()}
    </a>
  );
};

interface BlogPostProps {
  blog: {
    id: number;
    title: string;
    slug: string;
    excerpt: string;
    content: string;
    featured_image: string;
    featured_image_url?: string;
    category: string;
    is_published: boolean;
    published_at: string;
    author: {
      id: number;
      name: string;
      avatar?: string;
      bio?: string;
    };
  };
  relatedPosts: Array<{
    id: number;
    title: string;
    slug: string;
    featured_image: string;
    featured_image_url?: string;
    published_at: string;
  }>;
  seo: {
    title: string;
    description: string;
    keywords: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: string;
    ogUrl: string;
    structuredData: Record<string, any>;
  };
}

export default function BlogPost() {
  const { auth, blog, relatedPosts } = usePage<SharedData & BlogPostProps>().props;
  const formattedDate = blog.published_at ? format(new Date(blog.published_at), 'MMMM d, yyyy') : '';

  // Extract tags from content if needed
  const extractTags = (content: string): string[] => {
    // This is a simple implementation - you might want to enhance this
    // or store tags in a separate field in your database
    const commonTags = ['CSV', 'Data Transformation', 'Efficiency', 'Automation', 'Best Practices'];
    return commonTags.filter(tag => content.toLowerCase().includes(tag.toLowerCase()));
  };

  const tags = extractTags(blog.content);

  const { seo } = usePage<SharedData & BlogPostProps>().props;

  return (
    <>
      <AppPublicLayout user={auth.user}>
        <Seo
          title={seo.title}
          description={seo.description}
          keywords={seo.keywords}
          ogTitle={seo.ogTitle}
          ogDescription={seo.ogDescription}
          ogImage={seo.ogImage}
          ogUrl={seo.ogUrl}
          structuredData={seo.structuredData}
        />

        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="max-w-4xl mx-auto text-center">
              <div className="mb-4">
                <span className="inline-block text-sm font-medium text-primary bg-primary/10 px-3 py-1 rounded-full">
                  {blog.category || 'Uncategorized'}
                </span>
              </div>
              <h1 className="text-3xl md:text-5xl font-bold mb-6 font-serif">
                {blog.title}
              </h1>
              <div className="flex items-center justify-center mb-6">
                <img
                  src={blog.author.avatar || '/images/default-avatar.png'}
                  alt={blog.author.name}
                  className="w-10 h-10 rounded-full mr-3"
                />
                <div className="text-left">
                  <p className="font-medium">{blog.author.name}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{formattedDate}</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Image */}
        <section className="py-8 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="max-w-4xl mx-auto">
              {(blog.featured_image_url || blog.featured_image) && (
                <img
                  src={blog.featured_image_url || blog.featured_image}
                  alt={blog.title}
                  className="w-full h-auto rounded-lg shadow-sm"
                />
              )}
            </div>
          </div>
        </section>

        {/* Blog Content Section */}
        <section className="py-12 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Main Content */}
              <div className="w-full lg:w-3/4">
                <div className="max-w-3xl">
                  {/* Article Content */}
                  <article className="prose prose-lg max-w-none dark:prose-invert prose-headings:font-serif prose-a:text-primary">
                    <div dangerouslySetInnerHTML={{ __html: blog.content }} />
                  </article>

                  {/* Tags */}
                  {tags.length > 0 && (
                    <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-800">
                      <h3 className="text-lg font-bold mb-3">Tags:</h3>
                      <div className="flex flex-wrap gap-2">
                        {tags.map((tag, index) => (
                          <Link
                            key={index}
                            href={`/blog?tag=${tag}`}
                            className="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
                          >
                            {tag}
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Share */}
                  <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-800">
                    <h3 className="text-lg font-bold mb-3">Share this article:</h3>
                    <div className="flex space-x-3">
                      <SocialShareButton platform="twitter" url={`https://www.bulkifyconnect.com/blog/${blog.slug}`} />
                      <SocialShareButton platform="facebook" url={`https://www.bulkifyconnect.com/blog/${blog.slug}`} />
                      <SocialShareButton platform="linkedin" url={`https://www.bulkifyconnect.com/blog/${blog.slug}`} />
                    </div>
                  </div>

                  {/* Author Bio */}
                  <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-800">
                    <div className="flex items-start">
                      <img
                        src={blog.author.avatar || '/images/default-avatar.png'}
                        alt={blog.author.name}
                        className="w-16 h-16 rounded-full mr-4"
                      />
                      <div>
                        <h3 className="text-lg font-bold mb-2">About {blog.author.name}</h3>
                        <p className="text-gray-600 dark:text-gray-300">
                          {blog.author.bio || `${blog.author.name} is a contributor to our blog.`}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="w-full lg:w-1/4">
                {/* Related Posts */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-8">
                  <h3 className="text-xl font-bold mb-4">Related Posts</h3>
                  <div className="space-y-6">
                    {relatedPosts.map((relatedPost) => (
                      <RelatedPostCard
                        key={relatedPost.id}
                        title={relatedPost.title}
                        published_at={relatedPost.published_at}
                        featured_image={relatedPost.featured_image}
                        featured_image_url={relatedPost.featured_image_url}
                        slug={relatedPost.slug}
                      />
                    ))}
                  </div>
                </div>

                {/* Newsletter Signup */}
                <div className="bg-primary/10 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-4">Subscribe to Our Newsletter</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Get the latest CSV transformation tips and tricks delivered to your inbox.
                  </p>
                  <form className="space-y-3">
                    <input
                      type="email"
                      placeholder="Your email address"
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white"
                      required
                    />
                    <button
                      type="submit"
                      className="w-full px-4 py-2 bg-primary text-white font-medium rounded-md hover:bg-primary/90 transition-colors duration-200"
                    >
                      Subscribe
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* More Articles Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 font-serif">More Articles</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Explore more of our resources to help you master CSV data transformation.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {relatedPosts.map((relatedPost) => (
                <div key={relatedPost.id} className="bg-white dark:bg-gray-900 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
                  <Link href={`/blog/${relatedPost.slug}`}>
                    <img
                      src={relatedPost.featured_image_url || relatedPost.featured_image || '/images/blog/default.jpg'}
                      alt={relatedPost.title}
                      className="w-full h-48 object-cover"
                    />
                  </Link>
                  <div className="p-6">
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                      {formatDistanceToNow(new Date(relatedPost.published_at), { addSuffix: true })}
                    </p>
                    <Link href={`/blog/${relatedPost.slug}`}>
                      <h3 className="text-xl font-bold mb-2 hover:text-primary transition-colors duration-200">
                        {relatedPost.title}
                      </h3>
                    </Link>
                    <Link
                      href={`/blog/${relatedPost.slug}`}
                      className="text-primary font-medium text-sm hover:underline"
                    >
                      Read More
                    </Link>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center mt-12">
              <Link
                href="/blog"
                className="inline-block px-6 py-3 border border-primary text-primary font-medium rounded-md hover:bg-primary hover:text-white transition-colors duration-200"
              >
                View All Articles
              </Link>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-serif">Ready to Transform Your CSV Data?</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
              Start your 14-day free trial today and see how Bulkify Connect can streamline your workflow.
            </p>
            <a
              href="#"
              className="inline-block bg-primary text-white font-medium rounded-md px-8 py-3 text-lg hover:bg-primary/90 transition-colors duration-200"
            >
              Start Free Trial
            </a>
          </div>
        </section>
      </AppPublicLayout>
    </>
  );
}
