import React, { useState, useEffect } from 'react';
import AppPublicLayout from '@/layouts/app-public-layout';
import { type SharedData } from '@/types';
import { Link, router, usePage } from '@inertiajs/react';
import Seo from '@/components/Seo';
import { formatDistanceToNow } from 'date-fns';

// Blog Post Card Component
interface BlogPostCardProps {
  title: string;
  excerpt: string;
  published_at: string;
  category: string;
  featured_image: string;
  featured_image_url?: string;
  slug: string;
  author: {
    name: string;
    avatar?: string;
  };
}

const BlogPostCard: React.FC<BlogPostCardProps> = ({
  title,
  excerpt,
  published_at,
  category,
  featured_image,
  featured_image_url,
  slug,
  author
}) => {
  const formattedDate = formatDistanceToNow(new Date(published_at), { addSuffix: true });
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
      <Link href={`/blog/${slug}`}>
        <img
          src={featured_image_url || featured_image || '/images/blog/default.jpg'}
          alt={title}
          className="w-full h-48 object-cover"
        />
      </Link>
      <div className="p-6">
        <div className="flex justify-between items-center mb-3">
          <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
            {category}
          </span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {formattedDate}
          </span>
        </div>
        <Link href={`/blog/${slug}`}>
          <h3 className="text-xl font-bold mb-2 hover:text-primary transition-colors duration-200">
            {title}
          </h3>
        </Link>
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          {excerpt}
        </p>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <img
              src={author.avatar ? `/storage/${author.avatar}` : `/images/default-avatar.png`}
              alt={author.name}
              className="w-8 h-8 rounded-full mr-2"
            />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              {author.name}
            </span>
          </div>
          <Link
            href={`/blog/${slug}`}
            className="text-primary font-medium text-sm hover:underline"
          >
            Read More
          </Link>
        </div>
      </div>
    </div>
  );
};

// Category Filter Component
interface CategoryFilterProps {
  categories: string[];
  activeCategory: string;
  setActiveCategory: (category: string) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  activeCategory,
  setActiveCategory
}) => {
  return (
    <div className="flex flex-wrap gap-2 mb-8">
      <button
        className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
          activeCategory === 'all'
            ? 'bg-primary text-white'
            : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'
        }`}
        onClick={() => setActiveCategory('all')}
      >
        All
      </button>
      {categories.map((category) => (
        <button
          key={category}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
            activeCategory === category
              ? 'bg-primary text-white'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'
          }`}
          onClick={() => setActiveCategory(category)}
        >
          {category}
        </button>
      ))}
    </div>
  );
};

// Search Component
interface SearchProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}

const Search: React.FC<SearchProps> = ({ searchQuery, setSearchQuery }) => {
  return (
    <div className="relative mb-8">
      <input
        type="text"
        placeholder="Search articles..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        className="w-full px-4 py-3 pl-10 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white"
      />
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
        />
      </svg>
    </div>
  );
};

interface BlogPageProps {
  blogs: Array<{
    id: number;
    title: string;
    slug: string;
    excerpt: string;
    content: string;
    featured_image: string;
    featured_image_url?: string;
    category: string;
    is_published: boolean;
    published_at: string;
    author: {
      id: number;
      name: string;
      avatar?: string;
    };
  }>;
  categories: string[];
  filters: {
    category: string;
    search: string;
  };
}

export default function Blog() {
  const { auth, blogs, categories, filters } = usePage<SharedData & BlogPageProps>().props;
  const [activeCategory, setActiveCategory] = useState(filters.category || 'all');
  const [searchQuery, setSearchQuery] = useState(filters.search || '');

  // Update URL when filters change
  useEffect(() => {
    router.get(
      '/blog',
      { category: activeCategory, search: searchQuery },
      { preserveState: true, replace: true }
    );
  }, [activeCategory, searchQuery]);

  // Filter posts by category and search query
  const filteredPosts = blogs.filter(post => {
    const matchesCategory = activeCategory === 'all' || post.category === activeCategory;
    const matchesSearch = searchQuery === '' ||
                          post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          (post.excerpt && post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const { seo } = usePage<SharedData & BlogPageProps>().props;

  return (
    <>
      <AppPublicLayout user={auth.user}>
        <Seo
          title={seo.title}
          description={seo.description}
          keywords={seo.keywords}
          ogTitle={seo.ogTitle}
          ogDescription={seo.ogDescription}
          ogImage={seo.ogImage}
          ogUrl={seo.ogUrl}
          structuredData={seo.structuredData}
        />

        {/* Hero Section */}
        <section className="py-16 md:py-24 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-serif">Blog & Resources</h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Insights, tutorials, and best practices to help you master CSV data transformation.
            </p>
            <div className="w-24 h-1 bg-primary mx-auto"></div>
          </div>
        </section>

        {/* Blog Content Section */}
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Main Content */}
              <div className="w-full lg:w-3/4">
                {/* Search and Filters */}
                <Search searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
                <CategoryFilter
                  categories={categories}
                  activeCategory={activeCategory}
                  setActiveCategory={setActiveCategory}
                />

                {/* Blog Posts Grid */}
                {filteredPosts.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {filteredPosts.map((post) => (
                      <BlogPostCard
                        key={post.id}
                        title={post.title}
                        excerpt={post.excerpt || ''}
                        published_at={post.published_at}
                        category={post.category || 'Uncategorized'}
                        featured_image={post.featured_image_url || post.featured_image || '/images/blog/default.jpg'}
                        slug={post.slug}
                        author={post.author}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="text-xl font-bold mb-2">No posts found</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Try adjusting your search or filter to find what you're looking for.
                    </p>
                  </div>
                )}

                {/* Pagination */}
                {filteredPosts.length > 0 && (
                  <div className="flex justify-center mt-12">
                    <nav className="flex items-center space-x-2">
                      <button className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800">
                        Previous
                      </button>
                      <button className="px-4 py-2 bg-primary text-white rounded-md">
                        1
                      </button>
                      <button className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800">
                        2
                      </button>
                      <button className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800">
                        3
                      </button>
                      <button className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800">
                        Next
                      </button>
                    </nav>
                  </div>
                )}
              </div>

              {/* Sidebar */}
              <div className="w-full lg:w-1/4">
                {/* Popular Posts */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-8">
                  <h3 className="text-xl font-bold mb-4">Popular Posts</h3>
                  <ul className="space-y-4">
                    {blogs.slice(0, 3).map((post) => (
                      <li key={post.id} className="flex items-start">
                        <img
                          src={post.featured_image_url || post.featured_image || '/images/blog/default.jpg'}
                          alt={post.title}
                          className="w-16 h-16 object-cover rounded-md mr-3"
                        />
                        <div>
                          <Link
                            href={`/blog/${post.slug}`}
                            className="font-medium hover:text-primary transition-colors duration-200"
                          >
                            {post.title}
                          </Link>
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            {formatDistanceToNow(new Date(post.published_at), { addSuffix: true })}
                          </p>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Categories */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-8">
                  <h3 className="text-xl font-bold mb-4">Categories</h3>
                  <ul className="space-y-2">
                    {categories.map((category, index) => (
                      <li key={index}>
                        <button
                          className="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors duration-200"
                          onClick={() => setActiveCategory(category)}
                        >
                          {category}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Newsletter Signup */}
                <div className="bg-primary/10 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-4">Subscribe to Our Newsletter</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Get the latest CSV transformation tips and tricks delivered to your inbox.
                  </p>
                  <form className="space-y-3">
                    <input
                      type="email"
                      placeholder="Your email address"
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white"
                      required
                    />
                    <button
                      type="submit"
                      className="w-full px-4 py-2 bg-primary text-white font-medium rounded-md hover:bg-primary/90 transition-colors duration-200"
                    >
                      Subscribe
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-serif">Ready to Transform Your CSV Data?</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
              Start your 14-day free trial today and see how Bulkify Connect can streamline your workflow.
            </p>
            <a
              href="#"
              className="inline-block bg-primary text-white font-medium rounded-md px-8 py-3 text-lg hover:bg-primary/90 transition-colors duration-200"
            >
              Start Free Trial
            </a>
          </div>
        </section>
      </AppPublicLayout>
    </>
  );
}
