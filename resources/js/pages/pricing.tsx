import React, { useState } from 'react';
import AppPublicLayout from '@/layouts/app-public-layout';
import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import Seo from '@/components/Seo';

// Pricing Toggle Component
const PricingToggle: React.FC<{
  isAnnual: boolean;
  setIsAnnual: (value: boolean) => void;
}> = ({ isAnnual, setIsAnnual }) => {
  return (
    <div className="flex items-center justify-center space-x-4">
      <span className={`text-lg ${!isAnnual ? 'font-semibold text-primary' : 'text-gray-600 dark:text-gray-400'}`}>
        Monthly
      </span>
      <button
        type="button"
        className="relative inline-flex h-8 w-16 items-center rounded-full bg-gray-200 dark:bg-gray-700"
        onClick={() => setIsAnnual(!isAnnual)}
      >
        <span className="sr-only">Toggle billing frequency</span>
        <span
          className={`inline-block h-6 w-6 transform rounded-full bg-primary transition-transform ${
            isAnnual ? 'translate-x-9' : 'translate-x-1'
          }`}
        />
      </button>
      <span className={`text-lg ${isAnnual ? 'font-semibold text-primary' : 'text-gray-600 dark:text-gray-400'}`}>
        Annual <span className="text-sm text-green-600 dark:text-green-400 font-medium">Save 20%</span>
      </span>
    </div>
  );
};

// Pricing Plan Component
interface PricingPlanProps {
  title: string;
  price: {
    monthly: string;
    annual: string;
  };
  description: string;
  features: string[];
  isPopular?: boolean;
  buttonText: string;
  isAnnual: boolean;
}

const PricingPlan: React.FC<PricingPlanProps> = ({
  title,
  price,
  description,
  features,
  isPopular = false,
  buttonText,
  isAnnual
}) => {
  return (
    <div className={`bg-white dark:bg-[#1e2938] rounded-lg p-8 border ${isPopular ? 'border-primary shadow-md dark:border-primary' : 'border-gray-200 dark:border-gray-600'} relative transition-all duration-300 hover:shadow-lg dark:hover:shadow-xl`}>
      {isPopular && (
        <div className="absolute top-0 right-0 bg-primary text-white text-xs font-bold px-3 py-1 rounded-bl-lg rounded-tr-lg">
          MOST POPULAR
        </div>
      )}

      <h3 className="text-2xl font-bold mb-2 text-gray-900 dark:text-white">{title}</h3>
      <div className="mb-4">
        <span className="text-4xl font-bold text-gray-900 dark:text-white">{isAnnual ? price.annual : price.monthly}</span>
        <span className="text-gray-600 dark:text-gray-300">/{isAnnual ? 'year' : 'month'}</span>
      </div>
      <p className="text-gray-600 dark:text-gray-300 mb-6">{description}</p>

      <ul className="space-y-3 mb-8">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-gray-600 dark:text-gray-300">{feature}</span>
          </li>
        ))}
      </ul>

      <button className={`w-full py-3 rounded-md font-medium ${isPopular ? 'bg-primary text-white hover:bg-primary/90 dark:bg-primary dark:text-white dark:hover:bg-primary/90' : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-500'} transition-colors duration-200`}>
        {buttonText}
      </button>
    </div>
  );
};

// FAQ Item Component
interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer }) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 py-5">
      <button
        className="flex justify-between items-center w-full text-left"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{question}</h3>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <div className={`mt-3 text-gray-600 dark:text-gray-300 overflow-hidden transition-all duration-300 ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
        <p>{answer}</p>
      </div>
    </div>
  );
};

// Feature Comparison Table Component
interface FeatureComparisonProps {
  features: {
    name: string;
    basic: boolean | string;
    professional: boolean | string;
    enterprise: boolean | string;
  }[];
}

const FeatureComparison: React.FC<FeatureComparisonProps> = ({ features }) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Feature
            </th>
            <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Basic
            </th>
            <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Professional
            </th>
            <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Enterprise
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-[#1e2938] divide-y divide-gray-200 dark:divide-gray-600">
          {features.map((feature, index) => (
            <tr key={index} className={index % 2 === 0 ? 'bg-gray-50 dark:bg-gray-700' : 'bg-white dark:bg-[#1e2938]'}>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                {feature.name}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500 dark:text-gray-400">
                {typeof feature.basic === 'boolean' ? (
                  feature.basic ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  )
                ) : (
                  feature.basic
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500 dark:text-gray-400">
                {typeof feature.professional === 'boolean' ? (
                  feature.professional ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  )
                ) : (
                  feature.professional
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500 dark:text-gray-400">
                {typeof feature.enterprise === 'boolean' ? (
                  feature.enterprise ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  )
                ) : (
                  feature.enterprise
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default function Pricing() {
  const { auth, seo } = usePage<SharedData & { seo: any }>().props;
  const [isAnnual, setIsAnnual] = useState(false);

  // Pricing plans data
  const pricingPlans = [
    {
      title: "Basic",
      price: {
        monthly: "$9",
        annual: "$86"
      },
      description: "Perfect for individuals and small projects.",
      features: [
        "Up to 5,000 rows per import",
        "5 saved templates",
        "Basic transformations",
        "Email support",
        "1 user"
      ],
      isPopular: false,
      buttonText: "Get Started"
    },
    {
      title: "Professional",
      price: {
        monthly: "$29",
        annual: "$278"
      },
      description: "Ideal for growing businesses with regular CSV needs.",
      features: [
        "Up to 50,000 rows per import",
        "Unlimited saved templates",
        "Advanced transformations",
        "Priority email support",
        "API access",
        "5 users"
      ],
      isPopular: true,
      buttonText: "Get Started"
    },
    {
      title: "Enterprise",
      price: {
        monthly: "$99",
        annual: "$950"
      },
      description: "For large organizations with complex data needs.",
      features: [
        "Unlimited rows per import",
        "Unlimited saved templates",
        "Custom transformations",
        "24/7 priority support",
        "API access",
        "Dedicated account manager",
        "Unlimited users"
      ],
      isPopular: false,
      buttonText: "Contact Sales"
    }
  ];

  // Feature comparison data
  const featureComparison = [
    {
      name: "CSV Import",
      basic: true,
      professional: true,
      enterprise: true
    },
    {
      name: "CSV Export",
      basic: true,
      professional: true,
      enterprise: true
    },
    {
      name: "Basic Transformations",
      basic: true,
      professional: true,
      enterprise: true
    },
    {
      name: "Advanced Transformations",
      basic: false,
      professional: true,
      enterprise: true
    },
    {
      name: "Custom Transformations",
      basic: false,
      professional: false,
      enterprise: true
    },
    {
      name: "Saved Templates",
      basic: "5",
      professional: "Unlimited",
      enterprise: "Unlimited"
    },
    {
      name: "API Access",
      basic: false,
      professional: true,
      enterprise: true
    },
    {
      name: "Users",
      basic: "1",
      professional: "5",
      enterprise: "Unlimited"
    },
    {
      name: "Support",
      basic: "Email",
      professional: "Priority Email",
      enterprise: "24/7 Priority"
    },
    {
      name: "Dedicated Account Manager",
      basic: false,
      professional: false,
      enterprise: true
    }
  ];

  // FAQ data
  const faqs = [
    {
      question: "Can I change plans at any time?",
      answer: "Yes, you can upgrade or downgrade your plan at any time. When upgrading, you'll be charged the prorated difference for the remainder of your billing cycle. When downgrading, the new rate will apply at the start of your next billing cycle."
    },
    {
      question: "Is there a free trial available?",
      answer: "Yes! We offer a 14-day free trial of our Professional plan with no credit card required. This gives you full access to all features so you can thoroughly test our service with your own data."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards including Visa, Mastercard, American Express, and Discover. We also support payment via PayPal for monthly and annual subscriptions."
    },
    {
      question: "Can I cancel my subscription at any time?",
      answer: "Yes, you can cancel your subscription at any time from your account settings. Once canceled, you'll still have access to your plan until the end of your current billing period."
    },
    {
      question: "Do you offer discounts for non-profits or educational institutions?",
      answer: "Yes, we offer special pricing for non-profit organizations and educational institutions. Please contact our sales team for more information about our discount programs."
    }
  ];

  return (
    <>
      <AppPublicLayout user={auth.user}>
        <Seo
          title={seo.title}
          description={seo.description}
          keywords={seo.keywords}
          ogTitle={seo.ogTitle}
          ogDescription={seo.ogDescription}
          ogImage={seo.ogImage}
          ogUrl={seo.ogUrl}
          structuredData={seo.structuredData}
        />

        {/* Hero Section */}
        <section className="py-16 md:py-24 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-[#1e2938] dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-serif text-gray-900 dark:text-white">Simple, Transparent Pricing</h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Choose the plan that's right for you. All plans include a 14-day free trial.
            </p>
            <div className="w-24 h-1 bg-primary mx-auto"></div>
          </div>
        </section>

        {/* Pricing Toggle Section */}
        <section className="py-8 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <PricingToggle isAnnual={isAnnual} setIsAnnual={setIsAnnual} />
          </div>
        </section>

        {/* Pricing Plans Section */}
        <section className="py-12 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {pricingPlans.map((plan, index) => (
                <PricingPlan
                  key={index}
                  title={plan.title}
                  price={plan.price}
                  description={plan.description}
                  features={plan.features}
                  isPopular={plan.isPopular}
                  buttonText={plan.buttonText}
                  isAnnual={isAnnual}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Feature Comparison Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif text-gray-900 dark:text-white">Feature Comparison</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Compare our plans to find the perfect fit for your needs.
              </p>
            </div>

            <FeatureComparison features={featureComparison} />
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif text-gray-900 dark:text-white">Frequently Asked Questions</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Find answers to common questions about our pricing and plans.
              </p>
            </div>

            <div className="max-w-3xl mx-auto">
              {faqs.map((faq, index) => (
                <FAQItem
                  key={index}
                  question={faq.question}
                  answer={faq.answer}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Enterprise CTA Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="bg-white dark:bg-[#1e2938] rounded-lg shadow-sm p-8 md:p-12">
              <div className="flex flex-col md:flex-row items-center justify-between gap-8">
                <div>
                  <h2 className="text-2xl md:text-3xl font-bold mb-4 font-serif text-gray-900 dark:text-white">Need a custom solution?</h2>
                  <p className="text-gray-600 dark:text-gray-300 max-w-2xl">
                    Our Enterprise plan can be tailored to your specific requirements. Contact our sales team to discuss your needs.
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <a
                    href="#"
                    className="inline-block bg-primary text-white font-medium rounded-md px-8 py-3 text-lg hover:bg-primary/90 transition-colors duration-200 whitespace-nowrap"
                  >
                    Contact Sales
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Money-back Guarantee Section */}
        <section className="py-16 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-[#1e2938] dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <div className="inline-block bg-white dark:bg-[#1e2938] rounded-full p-4 mb-6 shadow-lg dark:shadow-xl">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-serif text-gray-900 dark:text-white">30-Day Money-Back Guarantee</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Try any plan risk-free. If you're not completely satisfied within the first 30 days, we'll refund your payment.
            </p>
            <a
              href="#"
              className="inline-block bg-primary text-white font-medium rounded-md px-8 py-3 text-lg hover:bg-primary/90 transition-colors duration-200"
            >
              Get Started Today
            </a>
          </div>
        </section>
      </AppPublicLayout>
    </>
  );
}
