import React, { useState } from 'react';
import AppPublicLayout from '@/layouts/app-public-layout';
import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import Seo from '@/components/Seo';
import axios from 'axios';

// Contact Info Card Component
interface ContactInfoCardProps {
  title: string;
  content: string;
  icon: React.ReactNode;
}

const ContactInfoCard: React.FC<ContactInfoCardProps> = ({ title, content, icon }) => {
  return (
    <div className="flex flex-col items-center text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
      <div className="bg-primary/10 rounded-lg w-16 h-16 flex items-center justify-center mb-4 text-primary">
        {icon}
      </div>
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      <p className="text-gray-600 dark:text-gray-300">{content}</p>
    </div>
  );
};

export default function Contact() {
  const { auth, seo } = usePage<SharedData & { seo: any }>().props;

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [formStatus, setFormStatus] = useState<{
    submitted: boolean;
    success: boolean;
    message: string;
  }>({
    submitted: false,
    success: false,
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Set submitting state
    setIsSubmitting(true);

    // Send the form data to the backend
    axios.post('/contact', formData)
      .then(response => {
        setFormStatus({
          submitted: true,
          success: true,
          message: response.data.message || 'Thank you for your message! We will get back to you soon.'
        });

        // Reset form after successful submission
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: ''
        });
      })
      .catch(error => {
        let errorMessage = 'There was an error sending your message. Please try again.';

        if (error.response && error.response.data && error.response.data.errors) {
          // Format validation errors
          const validationErrors = error.response.data.errors;
          errorMessage = Object.keys(validationErrors)
            .map(field => validationErrors[field].join('\n'))
            .join('\n');
        } else if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }

        setFormStatus({
          submitted: true,
          success: false,
          message: errorMessage
        });
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  // Contact info data
  const contactInfo = [
    {
      title: "Email Us",
      content: "<EMAIL>",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      title: "Call Us",
      content: "+923001066661",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
        </svg>
      )
    },
    {
      title: "Office Location",
      content: "123 CSV Avenue, Data City, CA 94123",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      )
    }
  ];

  return (
    <>
      <AppPublicLayout user={auth.user}>
        <Seo
          title={seo.title}
          description={seo.description}
          keywords={seo.keywords}
          ogTitle={seo.ogTitle}
          ogDescription={seo.ogDescription}
          ogImage={seo.ogImage}
          ogUrl={seo.ogUrl}
          structuredData={seo.structuredData}
        />

        {/* Hero Section */}
        <section className="py-16 md:py-24 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-serif">Contact Us</h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Have questions or need assistance? We're here to help you with all your CSV transformation needs.
            </p>
            <div className="w-24 h-1 bg-primary mx-auto"></div>
          </div>
        </section>

        {/* Contact Info Section */}
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">Get In Touch</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                We'd love to hear from you. Here's how you can reach us.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {contactInfo.map((info, index) => (
                <ContactInfoCard
                  key={index}
                  title={info.title}
                  content={info.content}
                  icon={info.icon}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Contact Form Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="max-w-3xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">Send Us a Message</h2>
                <p className="text-gray-600 dark:text-gray-300">
                  Fill out the form below and we'll get back to you as soon as possible.
                </p>
              </div>

              {formStatus.submitted && (
                <div className={`mb-8 p-4 rounded-md ${formStatus.success ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'}`}>
                  {formStatus.message}
                </div>
              )}

              <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-900 rounded-lg p-8 shadow-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Your Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Your Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white"
                      required
                    />
                  </div>
                </div>

                <div className="mb-6">
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Subject
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white"
                    required
                  >
                    <option value="">Select a subject</option>
                    <option value="General Inquiry">General Inquiry</option>
                    <option value="Technical Support">Technical Support</option>
                    <option value="Billing Question">Billing Question</option>
                    <option value="Feature Request">Feature Request</option>
                    <option value="Partnership">Partnership</option>
                  </select>
                </div>

                <div className="mb-6">
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Your Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows={6}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-800 dark:text-white"
                    required
                  ></textarea>
                </div>

                <div>
                  <button
                    type="submit"
                    className="w-full md:w-auto px-8 py-3 bg-primary text-white font-medium rounded-md hover:bg-primary/90 transition-colors duration-200 flex items-center justify-center"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </>
                    ) : (
                      'Send Message'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </section>

        {/* Map Section */}
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">Our Location</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Visit us at our office or reach out online. We're always happy to help.
              </p>
            </div>

            <div className="rounded-lg overflow-hidden shadow-sm h-96 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              {/* In a real application, you would embed a Google Map or similar here */}
              <div className="text-center p-8">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                </svg>
                <p className="text-gray-600 dark:text-gray-300 text-lg">
                  Interactive map would be displayed here in production.
                </p>
                <p className="text-gray-500 dark:text-gray-400 mt-2">
                  123 CSV Avenue, Data City, CA 94123
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-serif">Ready to Get Started?</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
              Transform your CSV data today with our powerful tools and expert support.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#"
                className="inline-block bg-primary text-white font-medium rounded-md px-8 py-3 text-lg hover:bg-primary/90 transition-colors duration-200"
              >
                Start Free Trial
              </a>
              <a
                href="#"
                className="inline-block bg-white text-primary border border-primary font-medium rounded-md px-8 py-3 text-lg hover:bg-gray-50 transition-colors duration-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                Schedule a Demo
              </a>
            </div>
          </div>
        </section>
      </AppPublicLayout>
    </>
  );
}
