import React from 'react';
import AppPublicLayout from '@/layouts/app-public-layout';
import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import Seo from '@/components/Seo';

// Service Card Component
interface ServiceCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
}

const ServiceCard: React.FC<ServiceCardProps> = ({ title, description, icon, features }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-8 transition-all duration-300 hover:shadow-md">
      <div className="bg-primary/10 rounded-lg w-16 h-16 flex items-center justify-center mb-6 text-primary">
        {icon}
      </div>
      <h3 className="text-2xl font-bold mb-3">{title}</h3>
      <p className="text-gray-600 dark:text-gray-300 mb-6">{description}</p>

      <h4 className="text-lg font-semibold mb-3">Features:</h4>
      <ul className="space-y-2">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-gray-600 dark:text-gray-300">{feature}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};



const PricingPlan: React.FC<PricingPlanProps> = ({
  title,
  price,
  period,
  description,
  features,
  isPopular = false,
  buttonText
}) => {
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg p-8 border ${isPopular ? 'border-primary' : 'border-gray-200 dark:border-gray-700'} relative`}>
      {isPopular && (
        <div className="absolute top-0 right-0 bg-primary text-white text-xs font-bold px-3 py-1 rounded-bl-lg rounded-tr-lg">
          MOST POPULAR
        </div>
      )}

      <h3 className="text-2xl font-bold mb-2">{title}</h3>
      <div className="mb-4">
        <span className="text-4xl font-bold">{price}</span>
        <span className="text-gray-600 dark:text-gray-300">/{period}</span>
      </div>
      <p className="text-gray-600 dark:text-gray-300 mb-6">{description}</p>

      <ul className="space-y-3 mb-8">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-gray-600 dark:text-gray-300">{feature}</span>
          </li>
        ))}
      </ul>

      <button className={`w-full py-3 rounded-md font-medium ${isPopular ? 'bg-primary text-white hover:bg-primary/90' : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600'} transition-colors duration-200`}>
        {buttonText}
      </button>
    </div>
  );
};

// FAQ Item Component
interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer }) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 py-5">
      <button
        className="flex justify-between items-center w-full text-left"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="text-xl font-semibold">{question}</h3>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <div className={`mt-3 text-gray-600 dark:text-gray-300 overflow-hidden transition-all duration-300 ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
        <p>{answer}</p>
      </div>
    </div>
  );
};

export default function Services() {
  const { auth, seo } = usePage<SharedData & { seo: any }>().props;

  // Services data
  const services = [
    {
      title: "CSV Import & Export",
      description: "Seamlessly import and export CSV files with advanced mapping capabilities.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
      ),
      features: [
        "Drag-and-drop file uploads",
        "Column mapping and validation",
        "Support for multiple file formats",
        "Batch processing capabilities"
      ]
    },
    {
      title: "Data Transformation",
      description: "Transform your CSV data with powerful tools and customizable templates.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      features: [
        "Field mapping and transformation",
        "Data cleansing and normalization",
        "Custom formula creation",
        "Conditional formatting"
      ]
    },
    {
      title: "Template Management",
      description: "Create and save templates for frequently used transformations.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      features: [
        "Save and reuse transformation settings",
        "Template sharing across team members",
        "Version control for templates",
        "Template categories and tags"
      ]
    }
  ];

  // Pricing plans data
  const pricingPlans = [
    {
      title: "Basic",
      price: "$9",
      period: "month",
      description: "Perfect for individuals and small projects.",
      features: [
        "Up to 5,000 rows per import",
        "5 saved templates",
        "Basic transformations",
        "Email support"
      ],
      isPopular: false,
      buttonText: "Get Started"
    },
    {
      title: "Professional",
      price: "$29",
      period: "month",
      description: "Ideal for growing businesses with regular CSV needs.",
      features: [
        "Up to 50,000 rows per import",
        "Unlimited saved templates",
        "Advanced transformations",
        "Priority email support",
        "API access"
      ],
      isPopular: true,
      buttonText: "Get Started"
    },
    {
      title: "Enterprise",
      price: "$99",
      period: "month",
      description: "For large organizations with complex data needs.",
      features: [
        "Unlimited rows per import",
        "Unlimited saved templates",
        "Custom transformations",
        "24/7 priority support",
        "API access",
        "Dedicated account manager"
      ],
      isPopular: false,
      buttonText: "Contact Sales"
    }
  ];

  // FAQ data
  const faqs = [
    {
      question: "What file formats do you support?",
      answer: "We primarily support CSV files, but we also support Excel (.xlsx, .xls), TSV, and other delimited text files. Our system automatically detects the file format and handles the import accordingly."
    },
    {
      question: "How secure is my data?",
      answer: "Your data security is our top priority. We use industry-standard encryption for all data transfers and storage. Your files are processed in isolated environments and are automatically deleted after processing unless you choose to save them in your account."
    },
    {
      question: "Can I try before I buy?",
      answer: "Yes! We offer a 14-day free trial of our Professional plan with no credit card required. This gives you full access to all features so you can thoroughly test our service with your own data."
    },
    {
      question: "Do you offer custom solutions?",
      answer: "Absolutely. Our Enterprise plan includes custom transformation options, and we can work with your team to develop specific solutions for your unique data challenges. Contact our sales team to discuss your requirements."
    },
    {
      question: "How do I get support if I have questions?",
      answer: "We offer multiple support channels including email support, an extensive knowledge base, and video tutorials. Professional and Enterprise plans include priority support with faster response times."
    }
  ];

  return (
    <>
      <AppPublicLayout user={auth.user}>
        <Seo
          title={seo.title}
          description={seo.description}
          keywords={seo.keywords}
          ogTitle={seo.ogTitle}
          ogDescription={seo.ogDescription}
          ogImage={seo.ogImage}
          ogUrl={seo.ogUrl}
          structuredData={seo.structuredData}
        />

        {/* Hero Section */}
        <section className="py-16 md:py-24 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-serif">Our Services</h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Comprehensive CSV transformation solutions to streamline your data workflow.
            </p>
            <div className="w-24 h-1 bg-primary mx-auto"></div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">What We Offer</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Our comprehensive suite of CSV transformation services designed to meet all your data needs.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <ServiceCard
                  key={index}
                  title={service.title}
                  description={service.description}
                  icon={service.icon}
                  features={service.features}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">How It Works</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Our simple three-step process makes CSV transformation quick and easy.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary text-white text-2xl font-bold mb-4">1</div>
                <h3 className="text-xl font-bold mb-2">Upload Your CSV</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Simply drag and drop your CSV file or select it from your computer.
                </p>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary text-white text-2xl font-bold mb-4">2</div>
                <h3 className="text-xl font-bold mb-2">Configure Transformations</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Set up your desired transformations using our intuitive interface.
                </p>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary text-white text-2xl font-bold mb-4">3</div>
                <h3 className="text-xl font-bold mb-2">Download Results</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Process your data and download the transformed CSV file instantly.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">Pricing Plans</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Choose the plan that best fits your needs. All plans include a 14-day free trial.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {pricingPlans.map((plan, index) => (
                <PricingPlan
                  key={index}
                  title={plan.title}
                  price={plan.price}
                  period={plan.period}
                  description={plan.description}
                  features={plan.features}
                  isPopular={plan.isPopular}
                  buttonText={plan.buttonText}
                />
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">Frequently Asked Questions</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Find answers to common questions about our services.
              </p>
            </div>

            <div className="max-w-3xl mx-auto">
              {faqs.map((faq, index) => (
                <FAQItem
                  key={index}
                  question={faq.question}
                  answer={faq.answer}
                />
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-serif">Ready to Transform Your CSV Data?</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
              Start your 14-day free trial today. No credit card required.
            </p>
            <a
              href="#"
              className="inline-block bg-primary text-white font-medium rounded-md px-8 py-3 text-lg hover:bg-primary/90 transition-colors duration-200"
            >
              Start Free Trial
            </a>
          </div>
        </section>
      </AppPublicLayout>
    </>
  );
}
