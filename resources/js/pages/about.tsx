import React from 'react';
import AppPublicLayout from '@/layouts/app-public-layout';
import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import Seo from '@/components/Seo';

// Team member component
interface TeamMemberProps {
  name: string;
  role: string;
  image: string;
}

const TeamMember: React.FC<TeamMemberProps> = ({ name, role, image }) => {
  return (
    <div className="flex flex-col items-center">
      <div className="w-32 h-32 rounded-full overflow-hidden mb-4 border-4 border-primary/20">
        <img src={image} alt={name} className="w-full h-full object-cover" />
      </div>
      <h3 className="text-xl font-bold">{name}</h3>
      <p className="text-gray-600 dark:text-gray-400">{role}</p>
    </div>
  );
};

// Value proposition component
interface ValueProps {
  title: string;
  description: string;
  icon: React.ReactNode;
}

const Value: React.FC<ValueProps> = ({ title, description, icon }) => {
  return (
    <div className="flex flex-col items-center text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
      <div className="bg-primary/10 rounded-lg w-16 h-16 flex items-center justify-center mb-4 text-primary">
        {icon}
      </div>
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      <p className="text-gray-600 dark:text-gray-400">{description}</p>
    </div>
  );
};

export default function About() {
  const { auth, seo } = usePage<SharedData & { seo: any }>().props;

  // Team members data
  const teamMembers = [
    {
      name: "Alex Johnson",
      role: "Founder & CEO",
      image: "https://randomuser.me/api/portraits/men/32.jpg"
    },
    {
      name: "Sarah Williams",
      role: "CTO",
      image: "https://randomuser.me/api/portraits/women/44.jpg"
    },
    {
      name: "Michael Chen",
      role: "Lead Developer",
      image: "https://randomuser.me/api/portraits/men/46.jpg"
    },
    {
      name: "Emily Rodriguez",
      role: "UX Designer",
      image: "https://randomuser.me/api/portraits/women/33.jpg"
    }
  ];

  // Company values data
  const values = [
    {
      title: "Innovation",
      description: "We constantly push the boundaries to create cutting-edge CSV transformation solutions.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    },
    {
      title: "Simplicity",
      description: "We believe in making complex data transformations simple and accessible for everyone.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    },
    {
      title: "Reliability",
      description: "Our platform is built with a focus on stability, security, and consistent performance.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      )
    }
  ];

  return (
    <>
      <AppPublicLayout user={auth.user}>
        <Seo
          title={seo.title}
          description={seo.description}
          keywords={seo.keywords}
          ogTitle={seo.ogTitle}
          ogDescription={seo.ogDescription}
          ogImage={seo.ogImage}
          ogUrl={seo.ogUrl}
          structuredData={seo.structuredData}
        />

        {/* Hero Section */}
        <section className="py-16 md:py-24 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 font-serif">About Bulkify Connect</h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              We're on a mission to simplify CSV data transformation for businesses of all sizes.
            </p>
            <div className="w-24 h-1 bg-primary mx-auto"></div>
          </div>
        </section>

        {/* Our Story Section */}
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="flex flex-col md:flex-row items-center gap-12">
              <div className="w-full md:w-1/2">
                <img
                  src="/images/about-story.svg"
                  alt="Our Story"
                  className="w-full h-auto rounded-lg"
                />
              </div>
              <div className="w-full md:w-1/2">
                <h2 className="text-3xl md:text-4xl font-bold mb-6 font-serif">Our Story</h2>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Bulkify Connect was founded in 2023 with a simple goal: to make CSV data transformation accessible to everyone. We noticed that many businesses were struggling with manual data entry and complex spreadsheet manipulations.
                </p>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Our team of data specialists and developers came together to create a solution that automates these tedious tasks, saving businesses countless hours and reducing errors.
                </p>
                <p className="text-gray-600 dark:text-gray-300">
                  Today, we're proud to serve clients across various industries, helping them streamline their data workflows and focus on what truly matters - growing their business.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Our Values Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">Our Values</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                These core principles guide everything we do at Bulkify Connect.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {values.map((value, index) => (
                <Value
                  key={index}
                  title={value.title}
                  description={value.description}
                  icon={value.icon}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif">Meet Our Team</h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                The talented people behind Bulkify Connect who make the magic happen.
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {teamMembers.map((member, index) => (
                <TeamMember
                  key={index}
                  name={member.name}
                  role={member.role}
                  image={member.image}
                />
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800">
          <div className="max-w-screen-2xl mx-auto w-full px-4 md:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-serif">Ready to Transform Your CSV Workflow?</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
              Join thousands of satisfied users who have simplified their data transformation process.
            </p>
            <a
              href="#"
              className="inline-block bg-primary text-white font-medium rounded-md px-8 py-3 text-lg hover:bg-primary/90 transition-colors duration-200"
            >
              Get Started Today
            </a>
          </div>
        </section>
      </AppPublicLayout>
    </>
  );
}
