// Welcome.tsx

import AppPublicLayout from '@/layouts/app-public-layout';
import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import Seo from '@/components/Seo';
import HeroSection from '@/components/HeroSection';
import OfferSection from '@/components/OfferSection';
import FeaturesCarousel from '@/components/FeaturesCarousel';
import BenefitsSection from '@/components/BenefitsSection';

export default function Welcome() {
    const { auth, seo } = usePage<SharedData & { seo: any }>().props;

    return (
        <>
            <AppPublicLayout user={auth.user}>
                <Seo
                    title={seo.title}
                    description={seo.description}
                    keywords={seo.keywords}
                    ogTitle={seo.ogTitle}
                    ogDescription={seo.ogDescription}
                    ogImage={seo.ogImage}
                    ogUrl={seo.ogUrl}
                    structuredData={seo.structuredData}
                >
                    <link rel="preconnect" href="https://fonts.bunny.net" />
                    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
                </Seo>
                <div className="flex min-h-screen flex-col w-full text-[#1b1b18] dark:text-[#EDEDEC] bg-gradient-to-br from-[#e6f7f1] to-white dark:bg-gradient-to-br dark:from-background dark:to-[#2a2839] transition-colors duration-200">
                    <HeroSection />
                    <OfferSection />
                    <div className="w-full max-w-screen-2xl mx-auto px-4 md:px-8">
                        <div className="border-b border-gray-200 dark:border-gray-700 opacity-30"></div>
                    </div>

                    <FeaturesCarousel />
                    <div className="w-full max-w-screen-2xl mx-auto px-4 md:px-8">
                        <div className="border-b border-gray-200 dark:border-gray-700 opacity-30"></div>
                    </div>
                    <BenefitsSection />
                </div>
            </AppPublicLayout>
        </>
    );
}
