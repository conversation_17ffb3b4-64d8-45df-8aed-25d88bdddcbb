import React, { useEffect, useState } from 'react';
import { Head, usePage, Link, router } from '@inertiajs/react';
import { type SharedData } from '@/types';
import { CsvTransformation } from '@/types/csv';
import AppDashboardLayout from '@/layouts/app-dashboard-layout';
import axios from 'axios';
import { formatDistance } from 'date-fns';
import { toast } from 'sonner';
import {
    BarChart3,
    FileText,
    ArrowUpRight,
    ArrowDownRight,
    Clock,
    CheckCircle2,
    AlertCircle,
    FileUp,
    FileDown,
    Repeat,
    BarChart4,
    PieChart,
    TrendingUp,
    Calendar,
    Plus
} from 'lucide-react';

// Stats Card Component
interface StatsCardProps {
    title: string;
    value: string;
    change: number;
    icon: React.ReactNode;
    iconBg: string;
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, change, icon, iconBg }) => {
    // Determine if we have valid change data
    const hasChangeData = change !== null && change !== undefined;

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</p>
                    <p className="text-2xl font-bold mt-1">{value}</p>
                </div>
                <div className={`p-3 rounded-full ${iconBg}`}>
                    {icon}
                </div>
            </div>
            <div className="mt-4 flex items-center">
                {hasChangeData ? (
                    <>
                        {change > 0 ? (
                            <div className="flex items-center text-green-500">
                                <ArrowUpRight className="h-4 w-4 mr-1" />
                                <span className="text-sm font-medium">{change}%</span>
                            </div>
                        ) : change < 0 ? (
                            <div className="flex items-center text-red-500">
                                <ArrowDownRight className="h-4 w-4 mr-1" />
                                <span className="text-sm font-medium">{Math.abs(change)}%</span>
                            </div>
                        ) : (
                            <div className="flex items-center text-gray-500">
                                <span className="text-sm font-medium">0%</span>
                            </div>
                        )}
                        <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">from last month</span>
                    </>
                ) : (
                    <span className="text-sm text-gray-500 dark:text-gray-400">No previous data for comparison</span>
                )}
            </div>
        </div>
    );
};

// Recent Activity Component
interface ActivityItemProps {
    title: string;
    description: string;
    time: string;
    icon: React.ReactNode;
    iconBg: string;
}

const ActivityItem: React.FC<ActivityItemProps> = ({ title, description, time, icon, iconBg }) => {
    return (
        <div className="flex items-start space-x-4 py-4">
            <div className={`p-2 rounded-full ${iconBg} flex-shrink-0`}>
                {icon}
            </div>
            <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{title}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{description}</p>
            </div>
            <div className="flex-shrink-0 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {time}
            </div>
        </div>
    );
};

// Recent Templates Component
interface TemplateItemProps {
    name: string;
    description: string;
    lastUsed: string;
    usageCount: number;
    onClick?: () => void;
}

const TemplateItem: React.FC<TemplateItemProps> = ({ name, description, lastUsed, usageCount, onClick }) => {
    return (
        <div className="flex items-center justify-between py-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700" onClick={onClick}>
            <div className="flex items-start space-x-4">
                <div className="p-2 rounded-md bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300">
                    <FileText className="h-5 w-5" />
                </div>
                <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{name}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{description}</p>
                </div>
            </div>
            <div className="text-right">
                <p className="text-sm text-gray-500 dark:text-gray-400">Last used {lastUsed}</p>
                <p className="text-sm font-medium text-gray-900 dark:text-white mt-1">Used {usageCount} times</p>
            </div>
        </div>
    );
};

// Transformation Status Component
interface TransformationStatusProps {
    initialData?: {
        completed: number;
        inProgress: number;
        failed: number;
        total: number;
    };
}

const TransformationStatus: React.FC<TransformationStatusProps> = ({ initialData }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [stats, setStats] = useState({
        completed: initialData?.completed || 0,
        inProgress: initialData?.inProgress || 0,
        failed: initialData?.failed || 0,
        total: initialData?.total || 0
    });

    useEffect(() => {
        const fetchStatistics = async () => {
            try {
                setIsLoading(true);
                const response = await axios.get(route('csv.transformations.statistics'));

                if (response.data.success) {
                    setStats({
                        completed: response.data.statistics.completed,
                        inProgress: response.data.statistics.in_progress,
                        failed: response.data.statistics.failed,
                        total: response.data.statistics.total
                    });
                } else {
                    setError('Failed to load transformation statistics');
                }
            } catch (err) {
                console.error('Error fetching transformation statistics:', err);
                setError('Failed to load transformation statistics');
            } finally {
                setIsLoading(false);
            }
        };

        fetchStatistics();
    }, []);

    const completedPercentage = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;
    const inProgressPercentage = stats.total > 0 ? Math.round((stats.inProgress / stats.total) * 100) : 0;
    const failedPercentage = stats.total > 0 ? Math.round((stats.failed / stats.total) * 100) : 0;

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">Transformation Status</h3>
                <select className="text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700">
                    <option>Last 7 days</option>
                    <option>Last 30 days</option>
                    <option>Last 90 days</option>
                </select>
            </div>

            {isLoading ? (
                <div className="flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#198856]"></div>
                </div>
            ) : error ? (
                <div className="text-center py-8 text-red-500">{error}</div>
            ) : (
                <>
                    <div className="space-y-4">
                        <div>
                            <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center">
                                    <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                                    <span className="text-sm font-medium">Completed</span>
                                </div>
                                <span className="text-sm font-medium">{stats.completed}</span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div className="bg-green-500 h-2 rounded-full" style={{ width: `${completedPercentage}%` }}></div>
                            </div>
                        </div>
                        <div>
                            <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center">
                                    <Clock className="h-4 w-4 text-yellow-500 mr-2" />
                                    <span className="text-sm font-medium">In Progress</span>
                                </div>
                                <span className="text-sm font-medium">{stats.inProgress}</span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div className="bg-yellow-500 h-2 rounded-full" style={{ width: `${inProgressPercentage}%` }}></div>
                            </div>
                        </div>
                        <div>
                            <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center">
                                    <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                                    <span className="text-sm font-medium">Failed</span>
                                </div>
                                <span className="text-sm font-medium">{stats.failed}</span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div className="bg-red-500 h-2 rounded-full" style={{ width: `${failedPercentage}%` }}></div>
                            </div>
                        </div>
                    </div>
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-500 dark:text-gray-400">Total Transformations</span>
                            <span className="text-lg font-bold">{stats.total}</span>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

// Data Usage Chart Component
interface DailyProcessingData {
    date: string;
    day: string;
    bytes_processed: number;
    files_processed: number;
}

const DataUsageChart: React.FC = () => {
    const [processingData, setProcessingData] = useState<DailyProcessingData[]>([]);
    const [timeRange, setTimeRange] = useState('7days');
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchProcessingData = async () => {
            try {
                setIsLoading(true);
                setError(null);
                const response = await axios.get(route('csv.transformations.statistics'));

                if (response.data.success && response.data.daily_processing_data) {
                    console.log('Daily processing data:', response.data.daily_processing_data);
                    setProcessingData(response.data.daily_processing_data);
                } else {
                    console.error('No daily processing data found in response:', response.data);
                    setError('Failed to load processing data');
                }
            } catch (error) {
                console.error('Error fetching processing data:', error);
                setError('Failed to load processing data');
            } finally {
                setIsLoading(false);
            }
        };

        fetchProcessingData();
    }, [timeRange]);

    // Ensure we have data to display
    const hasData = processingData.length > 0 && processingData.some(day => day.bytes_processed > 0);
    console.log('Has data:', hasData);

    // Find the maximum value to normalize the chart heights
    const maxBytes = hasData
        ? Math.max(...processingData.map(day => day.bytes_processed || 0), 1)
        : 1024 * 1024 * 10; // 10MB default if no data
    console.log('Max bytes for chart:', maxBytes);

    // Format bytes to human-readable format
    const formatBytes = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Calculate total data processed
    const totalBytesProcessed = processingData.reduce((sum, day) => sum + (day.bytes_processed || 0), 0);
    const totalFilesProcessed = processingData.reduce((sum, day) => sum + (day.files_processed || 0), 0);

    console.log('Total bytes processed:', totalBytesProcessed);
    console.log('Total files processed:', totalFilesProcessed);

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium">Data Processing</h3>
                <select
                    className="text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700"
                    value={timeRange}
                    onChange={(e) => setTimeRange(e.target.value)}
                >
                    <option value="7days">Last 7 days</option>
                    <option value="30days">Last 30 days</option>
                    <option value="90days">Last 90 days</option>
                </select>
            </div>

            {isLoading ? (
                <div className="h-64 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
            ) : error ? (
                <div className="h-64 flex items-center justify-center text-red-500">
                    {error}
                </div>
            ) : processingData.length === 0 ? (
                <div className="h-64 flex items-center justify-center text-gray-500">
                    <p>No data processing activity yet. Upload and process some CSV files to see data here.</p>
                </div>
            ) : (
                <>
                    <div className="h-64 flex items-end space-x-2">
                        {processingData.map((day, index) => {
                            // Calculate the height percentage for debugging
                            const heightPercentage = (day.bytes_processed / maxBytes) * 100;
                            console.log(`Bar ${index} (${day.day}):`, {
                                bytes: day.bytes_processed,
                                files: day.files_processed,
                                heightPercentage: heightPercentage + '%',
                                minHeight: day.bytes_processed > 0 ? '5%' : '0%'
                            });

                            return (
                                <div key={index} className="flex-1 flex flex-col items-center">
                                    <div
                                        className="w-full bg-blue-500 rounded-t-md transition-all duration-500 ease-in-out border border-blue-600"
                                        style={{
                                            height: `${heightPercentage}%`,
                                            minHeight: '8px' // Always show at least a small bar
                                        }}
                                        title={`${formatBytes(day.bytes_processed)} processed (${day.files_processed} files)`}
                                    ></div>
                                    <span className="text-xs mt-1">{day.day}</span>
                                </div>
                            );
                        })}
                    </div>

                    <div className="mt-6 grid grid-cols-2 gap-4">
                        <div className="flex items-center">
                            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                            <span className="text-sm text-gray-500 dark:text-gray-400">Processed: {formatBytes(totalBytesProcessed)}</span>
                        </div>
                        <div className="flex items-center">
                            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                            <span className="text-sm text-gray-500 dark:text-gray-400">Files: {totalFilesProcessed}</span>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

// Quick Actions Component
interface QuickActionProps {
    title: string;
    description: string;
    icon: React.ReactNode;
    iconBg: string;
    onClick: () => void;
}

const QuickAction: React.FC<QuickActionProps> = ({ title, description, icon, iconBg, onClick }) => {
    return (
        <button
            onClick={onClick}
            className="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow duration-200"
        >
            <div className={`p-3 rounded-full ${iconBg} mr-4`}>
                {icon}
            </div>
            <div className="text-left">
                <h3 className="text-sm font-medium">{title}</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{description}</p>
            </div>
        </button>
    );
};

// Activity item interface
interface ActivityItem {
    title: string;
    description: string;
    time: string;
    icon_type: 'check' | 'alert' | 'clock' | 'file';
    color: 'green' | 'red' | 'yellow' | 'blue';
    data: {
        id: number;
        type: string;
        [key: string]: any;
    };
}

// Template item interface
interface TemplateItem {
    id: number;
    name: string;
    description: string;
    lastUsed: string;
    usageCount: number;
}

export default function Dashboard() {
    const { auth } = usePage<SharedData>().props;
    const [recentTransformations, setRecentTransformations] = useState<CsvTransformation[]>([]);
    const [recentActivity, setRecentActivity] = useState<ActivityItem[]>([]);
    const [recentTemplates, setRecentTemplates] = useState<TemplateItem[]>([]);
    const [isLoadingTransformations, setIsLoadingTransformations] = useState(true);
    const [isLoadingActivity, setIsLoadingActivity] = useState(true);
    const [isLoadingTemplates, setIsLoadingTemplates] = useState(true);

    // Check for impersonation query parameter
    useEffect(() => {
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const impersonationStarted = urlParams.get('impersonation_started');
        const userName = urlParams.get('user');

        // If impersonation just started, show a notification
        if (impersonationStarted === '1' && userName) {
            toast.success(`You are now impersonating ${userName}`);

            // Remove the query parameters without triggering a page reload
            const url = new URL(window.location.href);
            url.searchParams.delete('impersonation_started');
            url.searchParams.delete('user');
            window.history.replaceState({}, '', url);
        }
    }, []);

    useEffect(() => {
        const fetchDashboardData = async () => {
            try {
                setIsLoadingTransformations(true);
                setIsLoadingActivity(true);
                setIsLoadingTemplates(true);
                const response = await axios.get(route('csv.transformations.statistics'));

                if (response.data.success) {
                    // Set recent transformations
                    if (response.data.recent_transformations) {
                        setRecentTransformations(response.data.recent_transformations);
                    }

                    // Set recent activity
                    if (response.data.recent_activity) {
                        setRecentActivity(response.data.recent_activity);
                    }

                    // Set recent templates
                    if (response.data.recent_templates) {
                        setRecentTemplates(response.data.recent_templates);
                    }
                }
            } catch (err) {
                console.error('Error fetching dashboard data:', err);
            } finally {
                setIsLoadingTransformations(false);
                setIsLoadingActivity(false);
                setIsLoadingTemplates(false);
            }
        };

        fetchDashboardData();
    }, []);

    // Empty array for activity when no data is available
    const emptyActivity: ActivityItem[] = [];

    // Helper function to get the appropriate icon based on the activity type
    const getActivityIcon = (activity: ActivityItem) => {
        if (activity.icon_type === 'check') {
            return <CheckCircle2 className={`h-5 w-5 text-${activity.color}-500`} />;
        } else if (activity.icon_type === 'alert') {
            return <AlertCircle className={`h-5 w-5 text-${activity.color}-500`} />;
        } else if (activity.icon_type === 'clock') {
            return <Clock className={`h-5 w-5 text-${activity.color}-500`} />;
        } else {
            return <FileText className={`h-5 w-5 text-${activity.color}-500`} />;
        }
    };

    // Helper function to get the appropriate background color based on the activity color
    const getActivityBg = (activity: ActivityItem) => {
        return `bg-${activity.color}-100 dark:bg-${activity.color}-900`;
    };

    // Empty array for templates when no data is available
    const emptyTemplates: TemplateItem[] = [];

    // Stats data - initialize with zeros for new users
    const [statsData, setStatsData] = useState({
        totalTransformations: 0,
        filesProcessed: 0,
        dataProcessed: 0,
        activeTemplates: 0,
        totalChange: null,
        filesProcessedChange: null,
        bytesProcessedChange: null,
        templatesChange: null
    });
    const [isLoadingStats, setIsLoadingStats] = useState(true);

    // Format bytes to human-readable format
    const formatBytes = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Fetch stats data
    useEffect(() => {
        const fetchStatsData = async () => {
            try {
                setIsLoadingStats(true);
                const response = await axios.get(route('csv.transformations.statistics'));

                if (response.data.success) {
                    // Check if daily_processing_data exists and has actual data
                    const hasProcessingData = response.data.daily_processing_data &&
                        Array.isArray(response.data.daily_processing_data) &&
                        response.data.daily_processing_data.length > 0 &&
                        response.data.daily_processing_data.some((day: any) =>
                            (day.bytes_processed && day.bytes_processed > 0) ||
                            (day.files_processed && day.files_processed > 0)
                        );

                    // Calculate total bytes processed only if we have actual data
                    const totalBytes = hasProcessingData
                        ? response.data.daily_processing_data.reduce((sum: number, day: any) => sum + (day.bytes_processed || 0), 0)
                        : 0;

                    // Calculate total files processed only if we have actual data
                    const totalFiles = hasProcessingData
                        ? response.data.daily_processing_data.reduce((sum: number, day: any) => sum + (day.files_processed || 0), 0)
                        : 0;

                    console.log('Stats data:', { hasProcessingData, totalBytes, totalFiles });

                    setStatsData({
                        totalTransformations: response.data.statistics.total || 0,
                        filesProcessed: totalFiles,
                        dataProcessed: totalBytes,
                        activeTemplates: response.data.recent_templates?.length || 0,
                        totalChange: response.data.statistics.total_change,
                        filesProcessedChange: response.data.statistics.files_processed_change,
                        bytesProcessedChange: response.data.statistics.bytes_processed_change,
                        templatesChange: response.data.statistics.templates_change
                    });
                }
            } catch (error) {
                console.error('Error fetching stats data:', error);
            } finally {
                setIsLoadingStats(false);
            }
        };

        fetchStatsData();
    }, []);

    return (
        <AppDashboardLayout user={auth.user}>
            <Head title="Dashboard" />

            <div className="mb-6">
                <h1 className="text-2xl font-bold">Welcome back, {auth.user?.name}!</h1>
                <p className="text-gray-500 dark:text-gray-400 mt-1">Here's what's happening with your data today.</p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <StatsCard
                    title="Total Transformations"
                    value={isLoadingStats ? "Loading..." : statsData.totalTransformations.toString()}
                    change={statsData.totalChange}
                    icon={<Repeat className="h-6 w-6 text-purple-500" />}
                    iconBg="bg-purple-100 dark:bg-purple-900"
                />
                <StatsCard
                    title="Files Processed"
                    value={isLoadingStats ? "Loading..." : statsData.filesProcessed.toString()}
                    change={statsData.filesProcessedChange}
                    icon={<FileText className="h-6 w-6 text-blue-500" />}
                    iconBg="bg-blue-100 dark:bg-blue-900"
                />
                <StatsCard
                    title="Data Processed"
                    value={isLoadingStats ? "Loading..." : formatBytes(statsData.dataProcessed)}
                    change={statsData.bytesProcessedChange}
                    icon={<BarChart3 className="h-6 w-6 text-green-500" />}
                    iconBg="bg-green-100 dark:bg-green-900"
                />
                <StatsCard
                    title="Active Templates"
                    value={isLoadingStats ? "Loading..." : statsData.activeTemplates.toString()}
                    change={statsData.templatesChange}
                    icon={<FileText className="h-6 w-6 text-orange-500" />}
                    iconBg="bg-orange-100 dark:bg-orange-900"
                />
            </div>

            {/* Main Content */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Left Column */}
                <div className="lg:col-span-2 space-y-8">
                    {/* CSV Processing Section */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between mb-6">
                            <h3 className="text-lg font-medium">CSV Processing</h3>
                            <Link
                                href={route('csv.upload.page')}
                                className="text-sm text-[#198856] hover:text-[#198856]/80"
                            >
                                Upload New CSV
                            </Link>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-100 dark:border-blue-800">
                                <div className="flex items-center justify-between">
                                    <h4 className="font-medium">Upload</h4>
                                    <FileUp className="h-5 w-5 text-blue-500" />
                                </div>
                                <p className="text-sm text-muted-foreground mt-2">Upload CSV files for processing</p>
                                <Link
                                    href={route('csv.upload.page')}
                                    className="text-sm text-blue-600 dark:text-blue-400 mt-4 inline-block"
                                >
                                    Upload CSV
                                </Link>
                            </div>

                            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-100 dark:border-green-800">
                                <div className="flex items-center justify-between">
                                    <h4 className="font-medium">Map & Transform</h4>
                                    <Repeat className="h-5 w-5 text-green-500" />
                                </div>
                                <p className="text-sm text-muted-foreground mt-2">Map columns and apply transformations</p>
                                <Link
                                    href={route('templates')}
                                    className="text-sm text-green-600 dark:text-green-400 mt-4 inline-block"
                                >
                                    View Templates
                                </Link>
                            </div>

                            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-100 dark:border-purple-800">
                                <div className="flex items-center justify-between">
                                    <h4 className="font-medium">Export & Share</h4>
                                    <FileDown className="h-5 w-5 text-purple-500" />
                                </div>
                                <p className="text-sm text-muted-foreground mt-2">Export transformed data in various formats</p>
                                <Link
                                    href={route('transformations.index')}
                                    className="text-sm text-purple-600 dark:text-purple-400 mt-4 inline-block"
                                >
                                    View Transformations
                                </Link>
                            </div>
                        </div>

                        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <h4 className="font-medium mb-3">Recent Transformations</h4>
                            {isLoadingTransformations ? (
                                <div className="flex justify-center items-center py-4">
                                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#198856]"></div>
                                </div>
                            ) : recentTransformations.length === 0 ? (
                                <div className="text-center py-4 text-gray-500">
                                    No transformations found
                                </div>
                            ) : (
                                <div className="space-y-2">
                                    {recentTransformations.slice(0, 2).map((transformation) => {
                                        // Determine icon and background based on status
                                        let icon = <Clock className="h-5 w-5 text-yellow-500" />;
                                        let iconBg = 'bg-yellow-100 dark:bg-yellow-900';

                                        if (transformation.status === 'completed') {
                                            icon = <CheckCircle2 className="h-5 w-5 text-green-500" />;
                                            iconBg = 'bg-green-100 dark:bg-green-900';
                                        } else if (transformation.status === 'failed') {
                                            icon = <AlertCircle className="h-5 w-5 text-red-500" />;
                                            iconBg = 'bg-red-100 dark:bg-red-900';
                                        }

                                        return (
                                            <div key={transformation.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                                <div className="flex items-center gap-3">
                                                    <div className={`p-2 rounded-full ${iconBg}`}>
                                                        {icon}
                                                    </div>
                                                    <div>
                                                        <p className="font-medium text-sm">{transformation.name}</p>
                                                        <p className="text-xs text-muted-foreground">
                                                            {formatDistance(new Date(transformation.created_at), new Date(), { addSuffix: true })}
                                                        </p>
                                                    </div>
                                                </div>
                                                <Link
                                                    href={route('transformations.index')}
                                                    className="text-xs text-[#198856] hover:text-[#198856]/80"
                                                >
                                                    View
                                                </Link>
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Data Usage Chart */}
                    <DataUsageChart />

                    {/* Quick Actions */}
                    <div>
                        <h2 className="text-lg font-medium mb-4">Quick Actions</h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <QuickAction
                                title="Upload CSV"
                                description="Transform a new CSV file"
                                icon={<FileUp className="h-5 w-5 text-blue-500" />}
                                iconBg="bg-blue-100 dark:bg-blue-900"
                                onClick={() => window.location.href = route('csv.upload.page')}
                            />
                            <QuickAction
                                title="Create Template"
                                description="Define a new transformation template"
                                icon={<Plus className="h-5 w-5 text-green-500" />}
                                iconBg="bg-green-100 dark:bg-green-900"
                                onClick={() => window.location.href = route('templates')}
                            />
                            <QuickAction
                                title="Schedule Job"
                                description="Set up a recurring transformation"
                                icon={<Calendar className="h-5 w-5 text-purple-500" />}
                                iconBg="bg-purple-100 dark:bg-purple-900"
                                onClick={() => window.location.href = route('transformations.index')}
                            />
                            <QuickAction
                                title="View Reports"
                                description="See transformation analytics"
                                icon={<BarChart4 className="h-5 w-5 text-orange-500" />}
                                iconBg="bg-orange-100 dark:bg-orange-900"
                                onClick={() => window.location.href = route('history')}
                            />
                        </div>
                    </div>

                    {/* Recent Templates */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium">Recent Templates</h3>
                            <button
                                onClick={() => window.location.href = route('csv.templates.index')}
                                className="text-sm text-[#198856] hover:text-[#198856]/80"
                            >
                                View all
                            </button>
                        </div>
                        {isLoadingTemplates ? (
                            <div className="flex justify-center items-center py-4">
                                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#198856]"></div>
                            </div>
                        ) : recentTemplates && recentTemplates.length > 0 ? (
                            <div className="divide-y divide-gray-200 dark:divide-gray-700">
                                {recentTemplates.map((template, index) => (
                                    <TemplateItem
                                        key={template.id}
                                        name={template.name}
                                        description={template.description}
                                        lastUsed={template.lastUsed}
                                        usageCount={template.usageCount}
                                        onClick={() => window.location.href = route('csv.templates.edit', { template: template.id })}
                                    />
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                                <FileText className="h-12 w-12 mx-auto mb-3 text-gray-400 dark:text-gray-600" />
                                <p>No templates found</p>
                                <p className="text-sm mt-1">Create a template to get started</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Right Column */}
                <div className="space-y-8">
                    {/* Transformation Status */}
                    <TransformationStatus />

                    {/* Recent Activity */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium">Recent Activity</h3>
                            <button
                                onClick={() => window.location.href = route('history')}
                                className="text-sm text-[#198856] hover:text-[#198856]/80"
                            >
                                View all
                            </button>
                        </div>
                        {isLoadingActivity ? (
                            <div className="flex justify-center items-center py-4">
                                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#198856]"></div>
                            </div>
                        ) : recentActivity && recentActivity.length > 0 ? (
                            <div className="divide-y divide-gray-200 dark:divide-gray-700">
                                {recentActivity.map((activity, index) => (
                                    <ActivityItem
                                        key={index}
                                        title={activity.title}
                                        description={activity.description}
                                        time={activity.time}
                                        icon={getActivityIcon(activity)}
                                        iconBg={getActivityBg(activity)}
                                    />
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                                <Clock className="h-12 w-12 mx-auto mb-3 text-gray-400 dark:text-gray-600" />
                                <p>No recent activity</p>
                                <p className="text-sm mt-1">Your activity will appear here</p>
                            </div>
                        )}
                    </div>

                    {/* Usage Limits */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium mb-4">Usage Limits</h3>
                        <div className="space-y-4">
                            <div>
                                <div className="flex items-center justify-between mb-1">
                                    <span className="text-sm font-medium">Transformations</span>
                                    <span className="text-sm font-medium">{statsData.totalTransformations} / 2,000</span>
                                </div>
                                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div className="bg-[#198856] h-2 rounded-full" style={{ width: `${Math.min((statsData.totalTransformations / 2000) * 100, 100)}%` }}></div>
                                </div>
                            </div>
                            <div>
                                <div className="flex items-center justify-between mb-1">
                                    <span className="text-sm font-medium">Storage</span>
                                    <span className="text-sm font-medium">{formatBytes(statsData.dataProcessed)} / 50 GB</span>
                                </div>
                                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div className="bg-[#198856] h-2 rounded-full" style={{ width: `${Math.min((statsData.dataProcessed / (50 * 1024 * 1024 * 1024)) * 100, 100)}%` }}></div>
                                </div>
                            </div>
                            <div>
                                <div className="flex items-center justify-between mb-1">
                                    <span className="text-sm font-medium">API Calls</span>
                                    <span className="text-sm font-medium">{statsData.filesProcessed * 10} / 10,000</span>
                                </div>
                                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div className="bg-[#198856] h-2 rounded-full" style={{ width: `${Math.min(((statsData.filesProcessed * 10) / 10000) * 100, 100)}%` }}></div>
                                </div>
                            </div>
                        </div>
                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <button className="text-sm text-[#198856] hover:text-[#198856]/80 font-medium">
                                Upgrade Plan
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </AppDashboardLayout>
    );
}
