<?php

namespace App\Helpers;

class Seo<PERSON><PERSON>per
{
    /**
     * Generate structured data for a website
     *
     * @return array
     */
    public static function getWebsiteSchema(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => config('app.name'),
            'url' => url('/'),
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => url('/search?q={search_term_string}'),
                'query-input' => 'required name=search_term_string',
            ],
        ];
    }

    /**
     * Generate structured data for an organization
     *
     * @return array
     */
    public static function getOrganizationSchema(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => config('app.name'),
            'url' => url('/'),
            'logo' => asset('images/logo.png'),
            'sameAs' => [
                // Add social media URLs here
                // 'https://twitter.com/bulkifyconnect',
                // 'https://facebook.com/bulkifyconnect',
                // 'https://linkedin.com/company/bulkifyconnect',
            ],
        ];
    }

    /**
     * Generate structured data for a blog post
     *
     * @param array $blog
     * @return array
     */
    public static function getBlogPostSchema(array $blog): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'BlogPosting',
            'headline' => $blog['title'],
            'description' => $blog['excerpt'] ?? '',
            'image' => $blog['featured_image_url'] ?? asset('images/blog/default.jpg'),
            'datePublished' => $blog['published_at'],
            'dateModified' => $blog['updated_at'],
            'author' => [
                '@type' => 'Person',
                'name' => $blog['author']['name'] ?? 'Bulkify Connect Team',
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => config('app.name'),
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => asset('images/logo.png'),
                ],
            ],
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => url('/blog/' . $blog['slug']),
            ],
        ];
    }

    /**
     * Generate structured data for a product
     *
     * @return array
     */
    public static function getProductSchema(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'SoftwareApplication',
            'name' => config('app.name'),
            'applicationCategory' => 'BusinessApplication',
            'operatingSystem' => 'Web',
            'offers' => [
                '@type' => 'Offer',
                'price' => '0',
                'priceCurrency' => 'USD',
            ],
        ];
    }
}
