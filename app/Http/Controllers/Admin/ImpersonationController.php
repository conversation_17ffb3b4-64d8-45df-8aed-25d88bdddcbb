<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class ImpersonationController extends Controller
{
    /**
     * Start impersonating a user
     */
    public function impersonate(Request $request, User $user): RedirectResponse
    {
        // Check if the current user is an admin
        if (!$request->user()->hasRole('Admin')) {
            abort(403, 'Unauthorized action.');
        }

        // Store the current admin's ID in the session
        Session::put('impersonator_id', $request->user()->id);

        // Login as the impersonated user
        Auth::login($user);

        // Use a direct URL redirect to force a full page reload
        $dashboardUrl = url('/dashboard');

        // Add a query parameter to indicate this is a fresh impersonation
        // This will be used by JavaScript to show a notification
        $redirectUrl = $dashboardUrl . '?impersonation_started=1&user=' . urlencode($user->name);

        return redirect($redirectUrl);
    }

    /**
     * Stop impersonating and return to admin account
     */
    public function stopImpersonating(Request $request): RedirectResponse
    {
        // Check if we're currently impersonating
        if (!Session::has('impersonator_id')) {
            return redirect()->back();
        }

        // Get the admin user
        $admin = User::findOrFail(Session::get('impersonator_id'));

        // Login as the admin again
        Auth::login($admin);

        // Remove the impersonator_id from the session
        Session::forget('impersonator_id');

        // Get the full URL to the Filament admin dashboard
        $adminUrl = url('/admin');

        // Redirect to the full admin URL instead of using a named route
        return redirect($adminUrl)
            ->with('success', 'You have stopped impersonating and returned to your admin account.');
    }
}
