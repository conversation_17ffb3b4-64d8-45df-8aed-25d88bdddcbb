<?php

namespace App\Http\Controllers;

use App\Helpers\SeoHelper;
use App\Models\Blog;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BlogController extends Controller
{
    /**
     * Display a listing of the blog posts.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $query = Blog::with('author')
            ->where('is_published', true)
            ->where('published_at', '<=', now());

        // Filter by category if provided
        if ($request->has('category') && $request->category !== 'all') {
            $query->where('category', $request->category);
        }

        // Search by title or content if search query is provided
        if ($request->has('search') && !empty($request->search)) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('content', 'like', '%' . $request->search . '%')
                  ->orWhere('excerpt', 'like', '%' . $request->search . '%');
            });
        }

        $blogs = $query->orderBy('published_at', 'desc')->get();

        // Get all unique categories for the filter
        $categories = Blog::where('is_published', true)
            ->where('published_at', '<=', now())
            ->whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->toArray();

        return Inertia::render('blog/index', [
            'blogs' => $blogs,
            'categories' => $categories,
            'filters' => [
                'category' => $request->category ?? 'all',
                'search' => $request->search ?? '',
            ],
            'seo' => [
                'title' => 'Blog - ' . config('app.name'),
                'description' => 'Explore our latest articles about CSV transformation, data processing, and more.',
                'keywords' => 'blog, CSV transformation, data processing, tutorials, tips, bulk data',
                'ogTitle' => 'Blog - ' . config('app.name'),
                'ogDescription' => 'Explore our latest articles about CSV transformation, data processing, and more.',
                'ogImage' => asset('images/blog/default.jpg'),
                'ogUrl' => route('blog'),
                'structuredData' => SeoHelper::getWebsiteSchema(),
            ],
        ]);
    }

    /**
     * Display the specified blog post.
     *
     * @param  string  $slug
     * @return \Inertia\Response
     */
    public function show($slug)
    {
        $blog = Blog::with('author')
            ->where('slug', $slug)
            ->where('is_published', true)
            ->where('published_at', '<=', now())
            ->firstOrFail();

        // Get related posts (same category, excluding current post)
        $relatedPosts = Blog::with('author')
            ->where('id', '!=', $blog->id)
            ->where('category', $blog->category)
            ->where('is_published', true)
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        // If we don't have enough related posts in the same category, get some recent posts
        if ($relatedPosts->count() < 3) {
            $additionalPosts = Blog::with('author')
                ->where('id', '!=', $blog->id)
                ->where('is_published', true)
                ->where('published_at', '<=', now())
                ->orderBy('published_at', 'desc')
                ->limit(3 - $relatedPosts->count())
                ->get();

            $relatedPosts = $relatedPosts->concat($additionalPosts);
        }

        // Prepare SEO data for the blog post
        $seoData = [
            'title' => $blog->title . ' - ' . config('app.name'),
            'description' => $blog->excerpt ?? substr(strip_tags($blog->content), 0, 160),
            'keywords' => $blog->category . ', CSV transformation, data processing, ' . config('app.name'),
            'ogTitle' => $blog->title,
            'ogDescription' => $blog->excerpt ?? substr(strip_tags($blog->content), 0, 160),
            'ogImage' => $blog->featured_image_url ?? asset('images/blog/default.jpg'),
            'ogUrl' => route('blog.show', $blog->slug),
            'structuredData' => SeoHelper::getBlogPostSchema($blog->toArray()),
        ];

        return Inertia::render('blog/show', [
            'blog' => $blog,
            'relatedPosts' => $relatedPosts,
            'seo' => $seoData,
        ]);
    }
}
