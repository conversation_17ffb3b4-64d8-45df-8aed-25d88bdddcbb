<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class GoogleAuthController extends Controller
{
    /**
     * Redirect the user to the Google authentication page.
     */
    public function redirectToGoogle(): RedirectResponse
    {
        return Socialite::driver('google')
            ->with([
                'prompt' => 'select_account',
                'access_type' => 'offline',
                'include_granted_scopes' => 'true'
            ])
            ->redirect();
    }

    /**
     * Handle the callback from Google.
     */
    public function handleGoogleCallback(Request $request): RedirectResponse
    {
        // Check if the user denied the authorization request
        if ($request->has('error') && $request->get('error') === 'access_denied') {
            return redirect()->route('login')
                ->with('error', 'You cancelled the Google sign-in process. Please try again or use email/password to log in.');
        }

        try {
            // Check if we have a code parameter
            if (!$request->has('code')) {
                throw new \Exception('Authorization code not provided. Please try again.');
            }

            $googleUser = Socialite::driver('google')->user();

            // Get user data from Google
            $googleId = $googleUser->getId();
            $name = $googleUser->getName();
            $email = $googleUser->getEmail();
            $avatar = $googleUser->getAvatar();
            $token = $googleUser->token;
            $refreshToken = $googleUser->refreshToken ?? null; // Handle case where refresh token might be null

            // Check if user already exists with this Google ID
            $user = User::where('google_id', $googleId)->first();

            // If user doesn't exist, check if email exists
            if (!$user) {
                $user = User::where('email', $email)->first();

                // If user with this email exists, update their Google ID
                if ($user) {
                    $user->update([
                        'google_id' => $googleId,
                        'google_token' => $token,
                        'google_refresh_token' => $refreshToken,
                        'avatar' => $avatar, // Now stored as TEXT in the database
                    ]);
                } else {
                    // Create a new user
                    $user = User::create([
                        'name' => $name,
                        'email' => $email,
                        'password' => Hash::make(Str::random(16)), // Random password
                        'google_id' => $googleId,
                        'google_token' => $token,
                        'google_refresh_token' => $refreshToken,
                        'avatar' => $avatar, // Now stored as TEXT in the database
                        'email_verified_at' => now(), // Automatically verify email for Google users
                    ]);

                    // Still fire the registered event, but don't need verification
                    event(new Registered($user));
                }
            } else {
                // Update existing user's Google tokens
                $user->update([
                    'google_token' => $token,
                    'google_refresh_token' => $refreshToken,
                    'avatar' => $avatar, // Now stored as TEXT in the database
                ]);
            }

            // Log the user in
            Auth::login($user);

            // For Google users, we'll automatically verify their email if it's not already verified
            if (!$user->hasVerifiedEmail()) {
                $user->markEmailAsVerified();
            }

            return redirect()->intended(route('dashboard', absolute: false));

        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Google authentication error: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            // Provide a user-friendly error message
            $errorMessage = 'Google authentication failed. ';

            if (str_contains($e->getMessage(), 'Data too long')) {
                $errorMessage .= 'There was an issue with your profile data. Please try again.';
            } else if (str_contains($e->getMessage(), 'invalid_request') || str_contains($e->getMessage(), 'Missing required parameter: code')) {
                $errorMessage = 'The authentication process was interrupted. Please try again.';
            } else if (str_contains($e->getMessage(), 'invalid_grant')) {
                $errorMessage = 'Your authentication session has expired. Please try again.';
            } else {
                // For other errors, provide a generic message in production
                $errorMessage = app()->environment('production')
                    ? 'An error occurred during authentication. Please try again.'
                    : $errorMessage . $e->getMessage();
            }

            return redirect()->route('login')->with('error', $errorMessage);
        }
    }
}
