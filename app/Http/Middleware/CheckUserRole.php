<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class CheckUserRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip this check if the user is being impersonated
        if (!Session::has('impersonator_id') && $request->user() && ($request->user()->hasRole('Admin') || $request->user()->hasRole('Sub-Admin'))) {
            // Check if the user is trying to access a regular application route
            $regularRoutes = ['dashboard', 'templates', 'data-sources', 'transformations.index', 'history'];

            if (in_array($request->route()->getName(), $regularRoutes)) {
                return redirect()->route('filament.admin.pages.dashboard')
                    ->with('warning', 'You are logged in as an ' . ($request->user()->hasRole('Admin') ? 'Admin' : 'Sub-Admin') .
                        '. Please use the admin panel to manage the application.');
            }
        }

        return $next($request);
    }
}
