<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectAdminToFilament
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if the user is authenticated and has admin privileges
        if (Auth::check() && ($request->user()->is_admin || $request->user()->hasRole('Admin') || $request->user()->hasRole('Sub-Admin'))) {
            // If the user is already logged in as an admin and trying to access the login page,
            // redirect them to the Filament admin dashboard
            return redirect()->route('filament.admin.pages.dashboard')
                ->with('info', 'You are already logged in as an ' .
                    ($request->user()->hasRole('Admin') ? 'Admin' : 'Sub-Admin') .
                    '. Please use the admin panel.');
        }

        return $next($request);
    }
}
