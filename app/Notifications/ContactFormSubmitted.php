<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ContactFormSubmitted extends Notification
{
    use Queueable;

    /**
     * The contact instance.
     *
     * @var \App\Models\Contact
     */
    protected $contact;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Contact  $contact
     * @return void
     */
    public function __construct($contact)
    {
        $this->contact = $contact;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Thank you for contacting Bulkify Connect')
            ->greeting('Hello ' . $this->contact->name . '!')
            ->line('Thank you for reaching out to us. We have received your message regarding "' . $this->contact->subject . '".')
            ->line('Our team will review your inquiry and get back to you as soon as possible.')
            ->line('For your reference, here is a copy of your message:')
            ->line('"' . $this->contact->message . '"')
            ->action('Visit Our Website', url('/'))
            ->line('If you have any additional questions, please don\'t hesitate to contact us again.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'contact_id' => $this->contact->id,
            'name' => $this->contact->name,
            'email' => $this->contact->email,
            'subject' => $this->contact->subject,
        ];
    }
}
