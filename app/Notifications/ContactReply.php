<?php

namespace App\Notifications;

use App\Models\Contact;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ContactReply extends Notification
{
    use Queueable;

    /**
     * The contact instance.
     *
     * @var \App\Models\Contact
     */
    protected $contact;

    /**
     * The reply message.
     *
     * @var string
     */
    protected $replyMessage;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Contact  $contact
     * @param  string  $replyMessage
     * @return void
     */
    public function __construct(Contact $contact, string $replyMessage)
    {
        $this->contact = $contact;
        $this->replyMessage = $replyMessage;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Re: ' . $this->contact->subject)
            ->greeting('Hello ' . $this->contact->name . '!')
            ->line('Thank you for contacting Bulkify Connect. Here is our response to your inquiry:')
            ->line($this->replyMessage)
            ->line('Your original message:')
            ->line('"' . $this->contact->message . '"')
            ->action('Visit Our Website', url('/'))
            ->line('If you have any additional questions, please don\'t hesitate to contact us again.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'contact_id' => $this->contact->id,
            'reply' => $this->replyMessage,
        ];
    }
}
