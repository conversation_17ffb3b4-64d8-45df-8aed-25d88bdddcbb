<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Inertia\Inertia;

class SeoServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share default SEO data with all views
        Inertia::share('seo', function () {
            return [
                'title' => config('app.name'),
                'description' => 'Bulkify Connect - Simplify CSV data transformation for businesses of all sizes',
                'keywords' => 'CSV transformation, data processing, bulk data, file conversion, data mapping',
                'ogTitle' => config('app.name'),
                'ogDescription' => 'Bulkify Connect - Simplify CSV data transformation for businesses of all sizes',
                'ogImage' => asset('images/logo.png'),
                'ogUrl' => url()->current(),
                'twitterCard' => 'summary_large_image',
                'twitterTitle' => config('app.name'),
                'twitterDescription' => 'Bulkify Connect - Simplify CSV data transformation for businesses of all sizes',
                'twitterImage' => asset('images/logo.png'),
            ];
        });
    }
}
