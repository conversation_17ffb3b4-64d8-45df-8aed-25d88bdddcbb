# Increase upload limits
<IfModule mod_php.c>
    php_value upload_max_filesize 100M
    php_value post_max_size 100M
    php_value memory_limit 256M
    php_value max_execution_time 300
    php_value max_input_time 300
</IfModule>

# Alternative method for FastCGI/PHP-FPM
<IfModule mod_fcgid.c>
    #FcgidMaxRequestLen 104857600
</IfModule>

# For mod_proxy_fcgi
<IfModule mod_proxy_fcgi.c>
    #ProxyFCGISetEnvVar PHP_VALUE "upload_max_filesize=100M\npost_max_size=100M\nmemory_limit=256M\nmax_execution_time=300\nmax_input_time=300"
</IfModule>

<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Handle X-XSRF-Token Header
    RewriteCond %{HTTP:x-xsrf-token} .
    RewriteRule .* - [E=HTTP_X_XSRF_TOKEN:%{HTTP:X-XSRF-Token}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
