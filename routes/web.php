<?php

use App\Helpers\SeoHelper;
use App\Http\Controllers\Admin\ImpersonationController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\ContactController;

Route::get('/', function () {
    return Inertia::render('home', [
        'seo' => [
            'title' => config('app.name') . ' - CSV Data Transformation Made Easy',
            'description' => 'Bulkify Connect simplifies CSV data transformation for businesses of all sizes. Import, transform, and export your data with ease.',
            'keywords' => 'CSV transformation, data processing, bulk data, file conversion, data mapping',
            'ogTitle' => config('app.name') . ' - CSV Data Transformation Made Easy',
            'ogDescription' => 'Bulkify Connect simplifies CSV data transformation for businesses of all sizes. Import, transform, and export your data with ease.',
            'ogImage' => asset('images/logo.png'),
            'ogUrl' => route('home'),
            'structuredData' => SeoHelper::getWebsiteSchema(),
        ],
    ]);
})->name('home');

Route::get('/about', function () {
    return Inertia::render('about', [
        'seo' => [
            'title' => 'About Us - ' . config('app.name'),
            'description' => 'Learn about Bulkify Connect, our mission, and how we help businesses simplify CSV data transformation.',
            'keywords' => 'about us, CSV transformation, data processing, company mission, team',
            'ogTitle' => 'About Us - ' . config('app.name'),
            'ogDescription' => 'Learn about Bulkify Connect, our mission, and how we help businesses simplify CSV data transformation.',
            'ogImage' => asset('images/about-story.svg'),
            'ogUrl' => route('about'),
            'structuredData' => SeoHelper::getOrganizationSchema(),
        ],
    ]);
})->name('about');

Route::get('/services', function () {
    return Inertia::render('services', [
        'seo' => [
            'title' => 'Services - ' . config('app.name'),
            'description' => 'Explore our comprehensive CSV transformation services including data import, export, mapping, and automation.',
            'keywords' => 'CSV services, data transformation, data mapping, automation, data processing',
            'ogTitle' => 'Services - ' . config('app.name'),
            'ogDescription' => 'Explore our comprehensive CSV transformation services including data import, export, mapping, and automation.',
            'ogImage' => asset('images/logo.png'),
            'ogUrl' => route('services'),
            'structuredData' => SeoHelper::getProductSchema(),
        ],
    ]);
})->name('services');

Route::get('/contact', function () {
    return Inertia::render('contact', [
        'seo' => [
            'title' => 'Contact Us - ' . config('app.name'),
            'description' => 'Get in touch with the Bulkify Connect team for questions, support, or partnership opportunities.',
            'keywords' => 'contact us, support, help, questions, CSV transformation',
            'ogTitle' => 'Contact Us - ' . config('app.name'),
            'ogDescription' => 'Get in touch with the Bulkify Connect team for questions, support, or partnership opportunities.',
            'ogImage' => asset('images/logo.png'),
            'ogUrl' => route('contact'),
            'structuredData' => SeoHelper::getOrganizationSchema(),
        ],
    ]);
})->name('contact');

Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

Route::get('/pricing', function () {
    return Inertia::render('pricing', [
        'seo' => [
            'title' => 'Pricing - ' . config('app.name'),
            'description' => 'View our flexible pricing plans for CSV data transformation services. Find the perfect plan for your business needs.',
            'keywords' => 'pricing, plans, subscription, CSV transformation, data processing',
            'ogTitle' => 'Pricing - ' . config('app.name'),
            'ogDescription' => 'View our flexible pricing plans for CSV data transformation services. Find the perfect plan for your business needs.',
            'ogImage' => asset('images/logo.png'),
            'ogUrl' => route('pricing'),
            'structuredData' => SeoHelper::getProductSchema(),
        ],
    ]);
})->name('pricing');

Route::get('/blog', [BlogController::class, 'index'])->name('blog');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');

Route::get('/roadmap', function () {
    return Inertia::render('roadmap');
})->name('roadmap');

Route::get('/privacy-policy', function () {
    return Inertia::render('privacy-policy', [
        'seo' => [
            'title' => 'Privacy Policy - ' . config('app.name'),
            'description' => 'Learn how Bulkify Connect collects, uses, and protects your personal information.',
            'keywords' => 'privacy policy, data protection, personal information, GDPR, privacy',
            'ogTitle' => 'Privacy Policy - ' . config('app.name'),
            'ogDescription' => 'Learn how Bulkify Connect collects, uses, and protects your personal information.',
            'ogImage' => asset('images/logo.png'),
            'ogUrl' => route('privacy.policy'),
        ],
    ]);
})->name('privacy.policy');

Route::get('/terms-of-service', function () {
    return Inertia::render('terms-of-service', [
        'seo' => [
            'title' => 'Terms of Service - ' . config('app.name'),
            'description' => 'Read the terms and conditions for using Bulkify Connect services.',
            'keywords' => 'terms of service, terms and conditions, legal, user agreement',
            'ogTitle' => 'Terms of Service - ' . config('app.name'),
            'ogDescription' => 'Read the terms and conditions for using Bulkify Connect services.',
            'ogImage' => asset('images/logo.png'),
            'ogUrl' => route('terms.service'),
        ],
    ]);
})->name('terms.service');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    Route::get('templates', function () {
        return Inertia::render('csv/templates');
    })->name('templates');

    Route::get('data-sources', function () {
        return Inertia::render('data-sources');
    })->name('data-sources');

    Route::get('transformations', [\App\Http\Controllers\CsvTransformationController::class, 'index'])->name('transformations.index');

    Route::get('history', [\App\Http\Controllers\CsvTransformationController::class, 'history'])->name('history');

    Route::get('documentation', function () {
        return Inertia::render('documentation');
    })->name('documentation');

    Route::get('settings', function () {
        return Inertia::render('settings');
    })->name('settings');

    Route::get('support', function () {
        return Inertia::render('support');
    })->name('support');

    // CSV Processing Routes
    Route::prefix('csv')->name('csv.')->group(function () {
        // CSV Upload
        Route::post('upload', [\App\Http\Controllers\CsvUploadController::class, 'upload'])->name('upload');
        Route::get('preview', [\App\Http\Controllers\CsvUploadController::class, 'preview'])->name('preview');
        Route::get('preview-progress', [\App\Http\Controllers\CsvUploadController::class, 'previewProgress'])->name('preview.progress');
        Route::post('parse-small', [\App\Http\Controllers\CsvUploadController::class, 'parseSmallFile'])->name('parse-small');

        // Large File Upload
        Route::post('large-upload/initialize', [\App\Http\Controllers\LargeFileUploadController::class, 'initialize'])->name('large-upload.initialize');
        Route::post('large-upload/chunk', [\App\Http\Controllers\LargeFileUploadController::class, 'uploadChunk'])->name('large-upload.chunk');

        // CSV Transformations
        Route::get('transformations', [\App\Http\Controllers\CsvTransformationController::class, 'index'])->name('transformations.index');
        Route::post('transformations', [\App\Http\Controllers\CsvTransformationController::class, 'store'])->name('transformations.store');
        Route::get('transformations/statistics', [\App\Http\Controllers\CsvTransformationController::class, 'getStatistics'])->name('transformations.statistics');
        Route::get('transformations/{transformation}', [\App\Http\Controllers\CsvTransformationController::class, 'show'])->name('transformations.show');
        Route::get('transformations/{transformation}/status', [\App\Http\Controllers\CsvTransformationController::class, 'status'])->name('transformations.status');
        Route::get('transformations/{transformation}/download', [\App\Http\Controllers\CsvTransformationController::class, 'download'])->name('transformations.download');
        Route::get('transformations/{transformation}/download-source', [\App\Http\Controllers\CsvTransformationController::class, 'downloadSource'])->name('transformations.download-source');
        Route::post('transformations/{transformation}/retry', [\App\Http\Controllers\CsvTransformationController::class, 'retry'])->name('transformations.retry');
        Route::delete('transformations/{transformation}', [\App\Http\Controllers\CsvTransformationController::class, 'destroy'])->name('transformations.destroy');
        Route::get('transformations/progress', [\App\Http\Controllers\CsvTransformationController::class, 'progress'])->name('transformations.progress');

        // File Information
        Route::get('file/info', [\App\Http\Controllers\CsvTransformationController::class, 'getFileInfo'])->name('file.info');
        Route::post('export', [\App\Http\Controllers\CsvTransformationController::class, 'export'])->name('export');
        Route::get('download', [\App\Http\Controllers\CsvTransformationController::class, 'getDownloadUrl'])->name('download');
        Route::get('file-download/{path}', [\App\Http\Controllers\CsvTransformationController::class, 'downloadFile'])->name('file.download');

        // CSV Templates
        Route::get('templates', [\App\Http\Controllers\CsvTemplateController::class, 'index'])->name('templates.index');
        Route::post('templates', [\App\Http\Controllers\CsvTemplateController::class, 'store'])->name('templates.store');
        Route::get('templates/{template}', [\App\Http\Controllers\CsvTemplateController::class, 'show'])->name('templates.show');
        Route::put('templates/{template}', [\App\Http\Controllers\CsvTemplateController::class, 'update'])->name('templates.update');
        Route::delete('templates/{template}', [\App\Http\Controllers\CsvTemplateController::class, 'destroy'])->name('templates.destroy');
        Route::get('templates-list', [\App\Http\Controllers\CsvTemplateController::class, 'list'])->name('templates.list');
        Route::post('templates/apply', [\App\Http\Controllers\CsvTemplateController::class, 'apply'])->name('templates.apply');

        // CSV Formulas
        Route::get('formulas/definitions', [\App\Http\Controllers\CsvFormulaController::class, 'definitions'])->name('formulas.definitions');
        Route::post('formulas/preview', [\App\Http\Controllers\CsvFormulaController::class, 'preview'])->name('formulas.preview');
        Route::post('formulas/preview-multiple', [\App\Http\Controllers\CsvFormulaController::class, 'previewMultiple'])->name('formulas.preview-multiple');

        // CSV Data Sources
        Route::get('data-sources', [\App\Http\Controllers\CsvDataSourceController::class, 'index'])->name('data-sources.index');
        Route::post('data-sources', [\App\Http\Controllers\CsvDataSourceController::class, 'store'])->name('data-sources.store');
        Route::get('data-sources/{dataSource}', [\App\Http\Controllers\CsvDataSourceController::class, 'show'])->name('data-sources.show');
        Route::put('data-sources/{dataSource}', [\App\Http\Controllers\CsvDataSourceController::class, 'update'])->name('data-sources.update');
        Route::delete('data-sources/{dataSource}', [\App\Http\Controllers\CsvDataSourceController::class, 'destroy'])->name('data-sources.destroy');
        Route::post('data-sources/{dataSource}/test', [\App\Http\Controllers\CsvDataSourceController::class, 'testConnection'])->name('data-sources.test');

        // CSV Pages
        Route::get('upload', function () {
            return Inertia::render('csv/upload');
        })->name('upload.page');

        Route::get('mapping', function () {
            return Inertia::render('csv/mapping');
        })->name('mapping.page');
    });
});

// Debug route to check PHP configuration
Route::get('/debug/php-info', function () {
    return response()->json([
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'max_input_time' => ini_get('max_input_time'),
    ]);
})->name('debug.php-info');

// User Impersonation Routes
Route::middleware(['auth'])->prefix('admin')->group(function () {
    Route::get('/impersonate/{user}', [ImpersonationController::class, 'impersonate'])->name('impersonate');
});

Route::middleware(['auth'])->group(function () {
    Route::get('/stop-impersonating', [ImpersonationController::class, 'stopImpersonating'])->name('stop-impersonating');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
