<?php

use App\Exceptions\Handler;
use App\Http\Middleware\CheckUserRole;
use App\Http\Middleware\HandleAppearance;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\ImpersonationMiddleware;
use App\Http\Middleware\RedirectAdminToFilament;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Foundation\Configuration\Scheduling;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: ['appearance']);

        // Register middleware aliases
        $middleware->alias([
            'RedirectAdminToFilament' => RedirectAdminToFilament::class,
        ]);

        $middleware->web(append: [
            HandleAppearance::class,
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
            CheckUserRole::class,
            ImpersonationMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Default Laravel exception handling
    })
    ->withSchedule(function ($schedule) {
        // Generate sitemap weekly
        $schedule->command('seo:sitemap')->weekly();
    })
    ->create();
